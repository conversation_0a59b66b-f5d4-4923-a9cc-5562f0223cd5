# ملف .htaccess لمنصة نبراس
# Nibrass Platform .htaccess Configuration

# تفعيل محرك إعادة الكتابة
RewriteEngine On

# إزالة .php من الروابط
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^([^\.]+)$ $1.php [NC,L]

# إعادة توجيه الصفحة الرئيسية
RewriteRule ^home$ index.php [NC,L]
RewriteRule ^الرئيسية$ index.php [NC,L]

# إعادة توجيه صفحات الفضاءات
RewriteRule ^student/?$ student-space/dashboard.php [NC,L]
RewriteRule ^parent/?$ parent-space/dashboard.php [NC,L]
RewriteRule ^specialist/?$ specialist-space/dashboard.php [NC,L]
RewriteRule ^admin/?$ admin/dashboard.php [NC,L]

# إعادة توجيه الصفحات العربية
RewriteRule ^من-نحن/?$ about.php [NC,L]
RewriteRule ^المكتبة/?$ library.php [NC,L]
RewriteRule ^الورشات/?$ workshops.php [NC,L]
RewriteRule ^الإبداع/?$ creativity.php [NC,L]
RewriteRule ^اتصل-بنا/?$ contact.php [NC,L]

# حماية الملفات الحساسة (مخفف للبيئة المحلية)
<Files "config.php">
    Require all denied
</Files>

<Files "db.php">
    Require all denied
</Files>

# منع عرض قائمة الملفات
Options -Indexes

# ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تخزين مؤقت للملفات الثابتة
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# إعدادات الأمان (مخففة للبيئة المحلية)
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# إعدادات PHP (إذا كان مسموحاً)
<IfModule mod_php.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 300
    php_value max_input_vars 3000
    php_flag display_errors On
    php_flag log_errors On
</IfModule>

# منع الوصول للمجلدات الحساسة
RedirectMatch 404 /\.git
RedirectMatch 404 /\.env
RedirectMatch 404 /composer\.

<?php
require_once 'config.php';
require_once 'db.php';

// إعداد متغيرات الصفحة
$page_title = 'اتصل بنا';
$page_description = 'تواصل مع فريق منصة نبراس للحصول على الدعم والمساعدة';

$success_message = '';
$error_message = '';

// معالجة إرسال الرسالة
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $name = clean_input($_POST['name']);
    $email = clean_input($_POST['email']);
    $subject = clean_input($_POST['subject']);
    $message = clean_input($_POST['message']);
    $type = clean_input($_POST['type']);
    
    if (empty($name) || empty($email) || empty($subject) || empty($message)) {
        $error_message = 'يرجى ملء جميع الحقول المطلوبة';
    } else {
        try {
            // البحث عن المستخدم أو إنشاء مستخدم مؤقت
            $user_id = null;
            $check_user = "SELECT id FROM users WHERE email = :email";
            $stmt = $db->prepare($check_user);
            $stmt->bindParam(':email', $email);
            $stmt->execute();
            
            if ($stmt->rowCount() > 0) {
                $user = $stmt->fetch();
                $user_id = $user['id'];
            } else {
                // إنشاء مستخدم مؤقت
                $temp_password = password_hash(uniqid(), PASSWORD_DEFAULT);
                $insert_user = "INSERT INTO users (name, email, password, role, is_active) VALUES (:name, :email, :password, 'parent', 0)";
                $stmt = $db->prepare($insert_user);
                $stmt->bindParam(':name', $name);
                $stmt->bindParam(':email', $email);
                $stmt->bindParam(':password', $temp_password);
                $stmt->execute();
                $user_id = $db->lastInsertId();
            }
            
            // إدراج الرسالة
            $insert_feedback = "INSERT INTO feedback (user_id, subject, message, type) VALUES (:user_id, :subject, :message, :type)";
            $stmt = $db->prepare($insert_feedback);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->bindParam(':subject', $subject);
            $stmt->bindParam(':message', $message);
            $stmt->bindParam(':type', $type);
            
            if ($stmt->execute()) {
                $success_message = 'تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.';
                // مسح البيانات
                $_POST = array();
            } else {
                $error_message = 'حدث خطأ أثناء إرسال الرسالة';
            }
        } catch (PDOException $e) {
            $error_message = 'حدث خطأ في النظام، يرجى المحاولة لاحقاً';
        }
    }
}

// تضمين الهيدر
include 'includes/header.php';
?>

    <style>
        .contact-header {
            background: linear-gradient(135deg, #17a2b8, #138496, #0c5460, #5bc0de);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
            color: white !important;
            padding: 3rem 0;
            border-radius: 0 0 30px 30px;
        }

        .contact-header h1,
        .contact-header p,
        .contact-header .lead {
            color: white !important;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .floating-shapes {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            overflow: hidden;
            pointer-events: none;
        }

        .shape {
            position: absolute;
            color: rgba(255,255,255,0.1);
            animation: float 6s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            top: 60%;
            right: 15%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            bottom: 30%;
            left: 20%;
            animation-delay: 4s;
        }

        .shape:nth-child(4) {
            top: 40%;
            right: 30%;
            animation-delay: 6s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>

    <!-- قسم العنوان المحسن -->
    <section class="contact-header position-relative">
        <div class="floating-shapes">
            <div class="shape"><i class="fas fa-envelope fa-3x"></i></div>
            <div class="shape"><i class="fas fa-phone fa-3x"></i></div>
            <div class="shape"><i class="fas fa-map-marker-alt fa-3x"></i></div>
            <div class="shape"><i class="fas fa-comments fa-3x"></i></div>
        </div>
        <div class="container">
            <div class="hero-content text-center">
                <h1><i class="fas fa-envelope"></i> اتصل بنا</h1>
                <p class="lead">نحن هنا لمساعدتك والإجابة على جميع استفساراتك</p>
                <div class="mt-4">
                    <span class="badge bg-light text-dark fs-6 me-2">
                        <i class="fas fa-phone"></i> هاتف
                    </span>
                    <span class="badge bg-light text-dark fs-6 me-2">
                        <i class="fas fa-envelope"></i> إيميل
                    </span>
                    <span class="badge bg-light text-dark fs-6 me-2">
                        <i class="fas fa-map-marker-alt"></i> عنوان
                    </span>
                    <span class="badge bg-light text-dark fs-6">
                        <i class="fas fa-clock"></i> 24/7
                    </span>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم معلومات الاتصال -->
    <section class="section">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mb-4">
                    <div class="card">
                        <div class="card-body p-4">
                            <h3 class="mb-4">
                                <i class="fas fa-envelope text-primary"></i>
                                أرسل لنا رسالة
                            </h3>

                            <?php if (!empty($success_message)): ?>
                                <div class="alert alert-success alert-arabic">
                                    <i class="fas fa-check-circle"></i>
                                    <?php echo $success_message; ?>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($error_message)): ?>
                                <div class="alert alert-danger alert-arabic">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <?php echo $error_message; ?>
                                </div>
                            <?php endif; ?>

                            <form method="POST" action="" class="form-arabic">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="name" class="form-label">
                                                <i class="fas fa-user"></i> الاسم الكامل *
                                            </label>
                                            <input type="text" class="form-control" id="name" name="name" 
                                                   value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>" 
                                                   required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="email" class="form-label">
                                                <i class="fas fa-envelope"></i> البريد الإلكتروني *
                                            </label>
                                            <input type="email" class="form-control" id="email" name="email" 
                                                   value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" 
                                                   required>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="type" class="form-label">
                                                <i class="fas fa-tag"></i> نوع الرسالة *
                                            </label>
                                            <select class="form-control" id="type" name="type" required>
                                                <option value="">اختر نوع الرسالة</option>
                                                <option value="question" <?php echo (isset($_POST['type']) && $_POST['type'] == 'question') ? 'selected' : ''; ?>>
                                                    استفسار عام
                                                </option>
                                                <option value="consultation" <?php echo (isset($_POST['type']) && $_POST['type'] == 'consultation') ? 'selected' : ''; ?>>
                                                    طلب استشارة
                                                </option>
                                                <option value="feedback" <?php echo (isset($_POST['type']) && $_POST['type'] == 'feedback') ? 'selected' : ''; ?>>
                                                    ملاحظات واقتراحات
                                                </option>
                                                <option value="complaint" <?php echo (isset($_POST['type']) && $_POST['type'] == 'complaint') ? 'selected' : ''; ?>>
                                                    شكوى
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="subject" class="form-label">
                                                <i class="fas fa-heading"></i> موضوع الرسالة *
                                            </label>
                                            <input type="text" class="form-control" id="subject" name="subject" 
                                                   value="<?php echo isset($_POST['subject']) ? htmlspecialchars($_POST['subject']) : ''; ?>" 
                                                   required>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group mb-4">
                                    <label for="message" class="form-label">
                                        <i class="fas fa-comment"></i> نص الرسالة *
                                    </label>
                                    <textarea class="form-control" id="message" name="message" rows="6" 
                                              placeholder="اكتب رسالتك هنا..." required><?php echo isset($_POST['message']) ? htmlspecialchars($_POST['message']) : ''; ?></textarea>
                                </div>

                                <button type="submit" class="btn btn-primary btn-arabic">
                                    <i class="fas fa-paper-plane"></i> إرسال الرسالة
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- معلومات الاتصال -->
                    <div class="card mb-4">
                        <div class="card-body text-center">
                            <h4 class="mb-4">
                                <i class="fas fa-phone text-primary"></i>
                                معلومات الاتصال
                            </h4>
                            
                            <div class="mb-3">
                                <i class="fas fa-envelope fa-2x text-primary mb-2"></i>
                                <h6>البريد الإلكتروني</h6>
                                <p><EMAIL><br><EMAIL></p>
                            </div>
                            
                            <div class="mb-3">
                                <i class="fas fa-phone fa-2x text-success mb-2"></i>
                                <h6>الهاتف</h6>
                                <p>+216 XX XXX XXX<br>+216 YY YYY YYY</p>
                            </div>
                            
                            <div class="mb-3">
                                <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                                <h6>ساعات العمل</h6>
                                <p>الأحد - الخميس<br>9:00 ص - 5:00 م</p>
                            </div>
                        </div>
                    </div>

                    <!-- وسائل التواصل الاجتماعي -->
                    <div class="card">
                        <div class="card-body text-center">
                            <h4 class="mb-4">
                                <i class="fas fa-share-alt text-primary"></i>
                                تابعنا على
                            </h4>
                            
                            <div class="d-flex justify-content-center gap-3">
                                <a href="#" class="btn btn-primary btn-sm">
                                    <i class="fab fa-facebook-f"></i>
                                </a>
                                <a href="#" class="btn btn-info btn-sm">
                                    <i class="fab fa-twitter"></i>
                                </a>
                                <a href="#" class="btn btn-danger btn-sm">
                                    <i class="fab fa-instagram"></i>
                                </a>
                                <a href="#" class="btn btn-success btn-sm">
                                    <i class="fab fa-whatsapp"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم الأسئلة الشائعة -->
    <section class="section" style="background-color: #f8f9fa;">
        <div class="container">
            <h2 class="section-title">الأسئلة الشائعة</h2>
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="accordion accordion-arabic" id="faqAccordion">
                        <div class="accordion-item mb-3">
                            <h2 class="accordion-header">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                    كيف يمكنني التسجيل في المنصة؟
                                </button>
                            </h2>
                            <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    يمكنك التسجيل بسهولة من خلال النقر على زر "إنشاء حساب" في أعلى الصفحة، ثم ملء البيانات المطلوبة واختيار نوع الحساب المناسب لك.
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item mb-3">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                    هل المنصة مجانية؟
                                </button>
                            </h2>
                            <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    نعم، معظم محتوى المنصة مجاني. هناك بعض الخدمات المتقدمة والاستشارات الخاصة التي قد تتطلب رسوماً رمزية.
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item mb-3">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                    كيف يمكنني الحصول على استشارة متخصصة؟
                                </button>
                            </h2>
                            <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    يمكنك طلب استشارة من خلال فضاء الأولياء بعد تسجيل الدخول، أو إرسال طلب استشارة من خلال نموذج الاتصال أعلاه.
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                                    هل يمكنني تحميل الموارد التعليمية؟
                                </button>
                            </h2>
                            <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    نعم، يمكن للمستخدمين المسجلين تحميل معظم الموارد التعليمية من مكتبة المنصة مجاناً.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

<?php
// تضمين الفوتر
include 'includes/footer.php';
?>

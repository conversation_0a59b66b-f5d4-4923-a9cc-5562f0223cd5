<?php
require_once 'config.php';
require_once 'db.php';

$success_message = '';
$error_message = '';

// معالجة إرسال الرسالة
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $name = clean_input($_POST['name']);
    $email = clean_input($_POST['email']);
    $subject = clean_input($_POST['subject']);
    $message = clean_input($_POST['message']);
    $type = clean_input($_POST['type']);
    
    if (empty($name) || empty($email) || empty($subject) || empty($message)) {
        $error_message = 'يرجى ملء جميع الحقول المطلوبة';
    } else {
        try {
            // البحث عن المستخدم أو إنشاء مستخدم مؤقت
            $user_id = null;
            $check_user = "SELECT id FROM users WHERE email = :email";
            $stmt = $db->prepare($check_user);
            $stmt->bindParam(':email', $email);
            $stmt->execute();
            
            if ($stmt->rowCount() > 0) {
                $user = $stmt->fetch();
                $user_id = $user['id'];
            } else {
                // إنشاء مستخدم مؤقت
                $temp_password = password_hash(uniqid(), PASSWORD_DEFAULT);
                $insert_user = "INSERT INTO users (name, email, password, role, is_active) VALUES (:name, :email, :password, 'parent', 0)";
                $stmt = $db->prepare($insert_user);
                $stmt->bindParam(':name', $name);
                $stmt->bindParam(':email', $email);
                $stmt->bindParam(':password', $temp_password);
                $stmt->execute();
                $user_id = $db->lastInsertId();
            }
            
            // إدراج الرسالة
            $insert_feedback = "INSERT INTO feedback (user_id, subject, message, type) VALUES (:user_id, :subject, :message, :type)";
            $stmt = $db->prepare($insert_feedback);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->bindParam(':subject', $subject);
            $stmt->bindParam(':message', $message);
            $stmt->bindParam(':type', $type);
            
            if ($stmt->execute()) {
                $success_message = 'تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.';
                // مسح البيانات
                $_POST = array();
            } else {
                $error_message = 'حدث خطأ أثناء إرسال الرسالة';
            }
        } catch (PDOException $e) {
            $error_message = 'حدث خطأ في النظام، يرجى المحاولة لاحقاً';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اتصل بنا - <?php echo SITE_NAME; ?></title>
    <meta name="description" content="تواصل مع فريق منصة نبراس للحصول على الدعم والمساعدة">
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="style.css" rel="stylesheet">
    <!-- RTL Enhancements -->
    <link href="rtl-enhancements.css" rel="stylesheet">
</head>
<body>
    <!-- الشريط العلوي -->
    <header class="header">
        <div class="container">
            <a href="index.php" class="logo">
                <i class="fas fa-star"></i> نبراس
            </a>
            <nav>
                <ul class="nav-menu">
                    <li><a href="index.php">الرئيسية</a></li>
                    <li><a href="about.php">من نحن</a></li>
                    <li><a href="library.php">المكتبة</a></li>
                    <li><a href="workshops.php">الورشات</a></li>
                    <li><a href="contact.php" class="active">اتصل بنا</a></li>
                    <li><a href="login.php" class="btn btn-secondary">تسجيل الدخول</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- قسم العنوان -->
    <section class="hero-section" style="padding: 2rem 0;">
        <div class="container">
            <div class="hero-content">
                <h1>اتصل بنا</h1>
                <p>نحن هنا لمساعدتك والإجابة على جميع استفساراتك</p>
            </div>
        </div>
    </section>

    <!-- قسم معلومات الاتصال -->
    <section class="section">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mb-4">
                    <div class="card">
                        <div class="card-body p-4">
                            <h3 class="mb-4">
                                <i class="fas fa-envelope text-primary"></i>
                                أرسل لنا رسالة
                            </h3>

                            <?php if (!empty($success_message)): ?>
                                <div class="alert alert-success alert-arabic">
                                    <i class="fas fa-check-circle"></i>
                                    <?php echo $success_message; ?>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($error_message)): ?>
                                <div class="alert alert-danger alert-arabic">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <?php echo $error_message; ?>
                                </div>
                            <?php endif; ?>

                            <form method="POST" action="" class="form-arabic">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="name" class="form-label">
                                                <i class="fas fa-user"></i> الاسم الكامل *
                                            </label>
                                            <input type="text" class="form-control" id="name" name="name" 
                                                   value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>" 
                                                   required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="email" class="form-label">
                                                <i class="fas fa-envelope"></i> البريد الإلكتروني *
                                            </label>
                                            <input type="email" class="form-control" id="email" name="email" 
                                                   value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" 
                                                   required>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="type" class="form-label">
                                                <i class="fas fa-tag"></i> نوع الرسالة *
                                            </label>
                                            <select class="form-control" id="type" name="type" required>
                                                <option value="">اختر نوع الرسالة</option>
                                                <option value="question" <?php echo (isset($_POST['type']) && $_POST['type'] == 'question') ? 'selected' : ''; ?>>
                                                    استفسار عام
                                                </option>
                                                <option value="consultation" <?php echo (isset($_POST['type']) && $_POST['type'] == 'consultation') ? 'selected' : ''; ?>>
                                                    طلب استشارة
                                                </option>
                                                <option value="feedback" <?php echo (isset($_POST['type']) && $_POST['type'] == 'feedback') ? 'selected' : ''; ?>>
                                                    ملاحظات واقتراحات
                                                </option>
                                                <option value="complaint" <?php echo (isset($_POST['type']) && $_POST['type'] == 'complaint') ? 'selected' : ''; ?>>
                                                    شكوى
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="subject" class="form-label">
                                                <i class="fas fa-heading"></i> موضوع الرسالة *
                                            </label>
                                            <input type="text" class="form-control" id="subject" name="subject" 
                                                   value="<?php echo isset($_POST['subject']) ? htmlspecialchars($_POST['subject']) : ''; ?>" 
                                                   required>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group mb-4">
                                    <label for="message" class="form-label">
                                        <i class="fas fa-comment"></i> نص الرسالة *
                                    </label>
                                    <textarea class="form-control" id="message" name="message" rows="6" 
                                              placeholder="اكتب رسالتك هنا..." required><?php echo isset($_POST['message']) ? htmlspecialchars($_POST['message']) : ''; ?></textarea>
                                </div>

                                <button type="submit" class="btn btn-primary btn-arabic">
                                    <i class="fas fa-paper-plane"></i> إرسال الرسالة
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- معلومات الاتصال -->
                    <div class="card mb-4">
                        <div class="card-body text-center">
                            <h4 class="mb-4">
                                <i class="fas fa-phone text-primary"></i>
                                معلومات الاتصال
                            </h4>
                            
                            <div class="mb-3">
                                <i class="fas fa-envelope fa-2x text-primary mb-2"></i>
                                <h6>البريد الإلكتروني</h6>
                                <p><EMAIL><br><EMAIL></p>
                            </div>
                            
                            <div class="mb-3">
                                <i class="fas fa-phone fa-2x text-success mb-2"></i>
                                <h6>الهاتف</h6>
                                <p>+216 XX XXX XXX<br>+216 YY YYY YYY</p>
                            </div>
                            
                            <div class="mb-3">
                                <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                                <h6>ساعات العمل</h6>
                                <p>الأحد - الخميس<br>9:00 ص - 5:00 م</p>
                            </div>
                        </div>
                    </div>

                    <!-- وسائل التواصل الاجتماعي -->
                    <div class="card">
                        <div class="card-body text-center">
                            <h4 class="mb-4">
                                <i class="fas fa-share-alt text-primary"></i>
                                تابعنا على
                            </h4>
                            
                            <div class="d-flex justify-content-center gap-3">
                                <a href="#" class="btn btn-primary btn-sm">
                                    <i class="fab fa-facebook-f"></i>
                                </a>
                                <a href="#" class="btn btn-info btn-sm">
                                    <i class="fab fa-twitter"></i>
                                </a>
                                <a href="#" class="btn btn-danger btn-sm">
                                    <i class="fab fa-instagram"></i>
                                </a>
                                <a href="#" class="btn btn-success btn-sm">
                                    <i class="fab fa-whatsapp"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم الأسئلة الشائعة -->
    <section class="section" style="background-color: #f8f9fa;">
        <div class="container">
            <h2 class="section-title">الأسئلة الشائعة</h2>
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="accordion accordion-arabic" id="faqAccordion">
                        <div class="accordion-item mb-3">
                            <h2 class="accordion-header">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                    كيف يمكنني التسجيل في المنصة؟
                                </button>
                            </h2>
                            <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    يمكنك التسجيل بسهولة من خلال النقر على زر "إنشاء حساب" في أعلى الصفحة، ثم ملء البيانات المطلوبة واختيار نوع الحساب المناسب لك.
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item mb-3">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                    هل المنصة مجانية؟
                                </button>
                            </h2>
                            <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    نعم، معظم محتوى المنصة مجاني. هناك بعض الخدمات المتقدمة والاستشارات الخاصة التي قد تتطلب رسوماً رمزية.
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item mb-3">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                    كيف يمكنني الحصول على استشارة متخصصة؟
                                </button>
                            </h2>
                            <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    يمكنك طلب استشارة من خلال فضاء الأولياء بعد تسجيل الدخول، أو إرسال طلب استشارة من خلال نموذج الاتصال أعلاه.
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                                    هل يمكنني تحميل الموارد التعليمية؟
                                </button>
                            </h2>
                            <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    نعم، يمكن للمستخدمين المسجلين تحميل معظم الموارد التعليمية من مكتبة المنصة مجاناً.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- التذييل -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div>
                    <h4>منصة نبراس</h4>
                    <p>منصة تعليمية متخصصة في دعم الأطفال ذوي اضطراب طيف التوحد وأسرهم</p>
                </div>
                <div>
                    <h4>روابط سريعة</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li><a href="index.php" style="color: #adb5bd; text-decoration: none;">الرئيسية</a></li>
                        <li><a href="about.php" style="color: #adb5bd; text-decoration: none;">من نحن</a></li>
                        <li><a href="library.php" style="color: #adb5bd; text-decoration: none;">المكتبة</a></li>
                        <li><a href="workshops.php" style="color: #adb5bd; text-decoration: none;">الورشات</a></li>
                    </ul>
                </div>
                <div>
                    <h4>تواصل معنا</h4>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +216 XX XXX XXX</p>
                    <div style="margin-top: 1rem;">
                        <a href="#" style="color: #adb5bd; margin-left: 1rem; font-size: 1.5rem;"><i class="fab fa-facebook"></i></a>
                        <a href="#" style="color: #adb5bd; margin-left: 1rem; font-size: 1.5rem;"><i class="fab fa-twitter"></i></a>
                        <a href="#" style="color: #adb5bd; margin-left: 1rem; font-size: 1.5rem;"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 منصة نبراس. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
/**
 * Header مشترك لجميع صفحات المنصة
 * Shared Header for all platform pages
 */

// تحديد الصفحة الحالية لتفعيل الرابط المناسب في القائمة
$current_page = basename($_SERVER['PHP_SELF'], '.php');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' . SITE_NAME : SITE_NAME; ?></title>
    <meta name="description" content="<?php echo isset($page_description) ? $page_description : 'منصة نبراس - منصة تعليمية متخصصة في دعم الأطفال ذوي اضطراب طيف التوحد وأسرهم'; ?>">
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="<?php echo get_asset_url('css/style.css'); ?>" rel="stylesheet">
    <link href="<?php echo get_asset_url('css/rtl-enhancements.css'); ?>" rel="stylesheet">
    
    <?php if (isset($additional_css)): ?>
        <?php foreach ($additional_css as $css_file): ?>
            <link href="<?php echo $css_file; ?>" rel="stylesheet">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body>
    <!-- الشريط العلوي المحسن -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top enhanced-navbar">
        <div class="container">
            <a class="navbar-brand logo-enhanced" href="<?php echo get_url('index.php'); ?>">
                <i class="fas fa-star logo-icon"></i>
                <span class="logo-text">نبراس</span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($current_page == 'index') ? 'active' : ''; ?>" href="<?php echo get_url('index.php'); ?>">
                            <i class="fas fa-home nav-icon"></i>
                            <span>الرئيسية</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($current_page == 'about') ? 'active' : ''; ?>" href="<?php echo get_url('about.php'); ?>">
                            <i class="fas fa-info-circle nav-icon"></i>
                            <span>من نحن</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($current_page == 'library') ? 'active' : ''; ?>" href="<?php echo get_url('library.php'); ?>">
                            <i class="fas fa-book nav-icon"></i>
                            <span>المكتبة</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($current_page == 'workshops') ? 'active' : ''; ?>" href="<?php echo get_url('workshops.php'); ?>">
                            <i class="fas fa-chalkboard-teacher nav-icon"></i>
                            <span>الورشات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($current_page == 'creativity') ? 'active' : ''; ?>" href="<?php echo get_url('creativity.php'); ?>">
                            <i class="fas fa-palette nav-icon"></i>
                            <span>الإبداع</span>
                        </a>
                    </li>
                    
                    <?php if (is_logged_in()): ?>
                        <?php $user = current_user(); ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user-circle nav-icon"></i>
                                <span><?php echo htmlspecialchars($user['name']); ?></span>
                            </a>
                            <ul class="dropdown-menu">
                                <?php if ($user['role'] == 'student'): ?>
                                    <li><a class="dropdown-item" href="<?php echo get_url('student-space/dashboard.php'); ?>">
                                        <i class="fas fa-child"></i> فضاء التلاميذ
                                    </a></li>
                                <?php elseif ($user['role'] == 'parent'): ?>
                                    <li><a class="dropdown-item" href="<?php echo get_url('parent-space/dashboard.php'); ?>">
                                        <i class="fas fa-user-friends"></i> فضاء الأولياء
                                    </a></li>
                                <?php elseif ($user['role'] == 'specialist'): ?>
                                    <li><a class="dropdown-item" href="<?php echo get_url('specialist-space/dashboard.php'); ?>">
                                        <i class="fas fa-user-md"></i> فضاء الأخصائيين
                                    </a></li>
                                <?php elseif ($user['role'] == 'admin'): ?>
                                    <li><a class="dropdown-item" href="<?php echo get_url('admin/dashboard.php'); ?>">
                                        <i class="fas fa-cogs"></i> لوحة الإدارة
                                    </a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo get_url('ai-chat.php'); ?>">
                                    <i class="fas fa-robot"></i> المساعد الذكي
                                </a></li>
                                <li><a class="dropdown-item" href="<?php echo get_url('logout.php'); ?>">
                                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                                </a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'contact') ? 'active' : ''; ?>" href="<?php echo get_url('contact.php'); ?>">
                                <i class="fas fa-envelope nav-icon"></i>
                                <span>اتصل بنا</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link btn-login <?php echo ($current_page == 'login') ? 'active' : ''; ?>" href="<?php echo get_url('login.php'); ?>">
                                <i class="fas fa-sign-in-alt nav-icon"></i>
                                <span>تسجيل الدخول</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link btn-register <?php echo ($current_page == 'register') ? 'active' : ''; ?>" href="<?php echo get_url('register.php'); ?>">
                                <i class="fas fa-user-plus nav-icon"></i>
                                <span>إنشاء حساب</span>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- رسائل النظام -->
    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="container mt-3">
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle"></i>
                <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="container mt-3">
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle"></i>
                <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    <?php endif; ?>

    <!-- رسالة تسجيل الخروج -->
    <?php if (isset($_GET['logged_out']) && $_GET['logged_out'] == '1'): ?>
        <div class="container mt-3">
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle"></i>
                تم تسجيل الخروج بنجاح. نراك قريباً!
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    <?php endif; ?>

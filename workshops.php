<?php
require_once 'config.php';
require_once 'db.php';

// جلب الورشات المتاحة
$workshops_query = "SELECT w.*, u.name as instructor_name FROM workshops w 
                    JOIN users u ON w.instructor_id = u.id 
                    WHERE w.is_active = 1 AND w.date >= NOW() 
                    ORDER BY w.date ASC";
$workshops_stmt = $db->prepare($workshops_query);
$workshops_stmt->execute();
$workshops = $workshops_stmt->fetchAll();

// جلب الورشات السابقة (للمراجع)
$past_workshops_query = "SELECT w.*, u.name as instructor_name FROM workshops w 
                         JOIN users u ON w.instructor_id = u.id 
                         WHERE w.is_active = 1 AND w.date < NOW() 
                         ORDER BY w.date DESC LIMIT 6";
$past_workshops_stmt = $db->prepare($past_workshops_query);
$past_workshops_stmt->execute();
$past_workshops = $past_workshops_stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الورشات التفاعلية - <?php echo SITE_NAME; ?></title>
    <meta name="description" content="ورشات تدريبية متخصصة في التوحد للأولياء والأخصائيين">
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="style.css" rel="stylesheet">
    <link href="rtl-enhancements.css" rel="stylesheet">
    
    <style>
        .workshop-card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        .workshop-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        
        .workshop-header {
            background: linear-gradient(135deg, #4caf50, #8bc34a);
            color: white;
            padding: 1.5rem;
            position: relative;
        }
        
        .workshop-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 20px;
            background: white;
            border-radius: 20px 20px 0 0;
        }
        
        .workshop-date {
            background: rgba(255,255,255,0.2);
            border-radius: 15px;
            padding: 0.5rem 1rem;
            display: inline-block;
            margin-bottom: 1rem;
        }
        
        .instructor-badge {
            background: var(--nibrass-orange);
            color: white;
            padding: 0.3rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }
        
        .participants-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .register-btn {
            background: linear-gradient(135deg, #ff9800, #ff5722);
            border: none;
            border-radius: 25px;
            padding: 0.75rem 2rem;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 152, 0, 0.3);
            color: white;
        }
        
        .past-workshop {
            opacity: 0.8;
            border: 2px solid #e9ecef;
        }
        
        .workshop-category {
            position: absolute;
            top: 1rem;
            left: 1rem;
            background: rgba(255,255,255,0.9);
            color: var(--nibrass-blue);
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <!-- الشريط العلوي -->
    <header class="header">
        <div class="container">
            <a href="index.php" class="logo">
                <i class="fas fa-star"></i> نبراس
            </a>
            <nav>
                <ul class="nav-menu">
                    <li><a href="index.php">الرئيسية</a></li>
                    <li><a href="about.php">من نحن</a></li>
                    <li><a href="library.php">المكتبة</a></li>
                    <li><a href="workshops.php" class="active">الورشات</a></li>
                    <li><a href="contact.php">اتصل بنا</a></li>
                    <li><a href="login.php" class="btn btn-secondary">تسجيل الدخول</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- قسم العنوان -->
    <section class="hero-section" style="padding: 2rem 0;">
        <div class="container">
            <div class="hero-content">
                <h1>الورشات التفاعلية</h1>
                <p>ورشات تدريبية متخصصة لتطوير مهاراتك في التعامل مع التوحد</p>
            </div>
        </div>
    </section>

    <!-- الورشات القادمة -->
    <section class="section">
        <div class="container">
            <h2 class="section-title">الورشات القادمة</h2>
            <?php if (!empty($workshops)): ?>
                <div class="row">
                    <?php foreach ($workshops as $workshop): ?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card workshop-card">
                                <div class="workshop-header">
                                    <div class="workshop-category">ورشة تدريبية</div>
                                    <div class="workshop-date">
                                        <i class="fas fa-calendar"></i>
                                        <?php echo date('Y/m/d', strtotime($workshop['date'])); ?>
                                        <br>
                                        <i class="fas fa-clock"></i>
                                        <?php echo date('H:i', strtotime($workshop['date'])); ?>
                                    </div>
                                    <h5><?php echo htmlspecialchars($workshop['title']); ?></h5>
                                </div>
                                
                                <div class="card-body">
                                    <p class="card-text"><?php echo htmlspecialchars($workshop['description']); ?></p>
                                    
                                    <div class="participants-info">
                                        <div class="row text-center">
                                            <div class="col-4">
                                                <i class="fas fa-clock text-primary"></i>
                                                <div><strong><?php echo $workshop['duration']; ?></strong></div>
                                                <small>دقيقة</small>
                                            </div>
                                            <div class="col-4">
                                                <i class="fas fa-users text-success"></i>
                                                <div><strong><?php echo $workshop['max_participants']; ?></strong></div>
                                                <small>مشارك</small>
                                            </div>
                                            <div class="col-4">
                                                <i class="fas fa-user-tie text-warning"></i>
                                                <div><strong>مجاني</strong></div>
                                                <small>التكلفة</small>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="instructor-badge">
                                            <i class="fas fa-chalkboard-teacher"></i>
                                            <?php echo htmlspecialchars($workshop['instructor_name']); ?>
                                        </span>
                                        <button class="btn register-btn" onclick="registerWorkshop(<?php echo $workshop['id']; ?>)">
                                            <i class="fas fa-user-plus"></i> سجل الآن
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle fa-3x mb-3"></i>
                    <h4>لا توجد ورشات متاحة حالياً</h4>
                    <p>سيتم الإعلان عن الورشات الجديدة قريباً. تابعنا للحصول على آخر التحديثات.</p>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- أنواع الورشات -->
    <section class="section" style="background-color: #f8f9fa;">
        <div class="container">
            <h2 class="section-title">أنواع الورشات المتاحة</h2>
            <div class="row">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card text-center h-100">
                        <div class="card-body">
                            <i class="fas fa-users fa-3x text-primary mb-3"></i>
                            <h5>ورشات للأولياء</h5>
                            <p>تعلم كيفية التعامل مع طفلك وتطوير مهاراته اليومية</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card text-center h-100">
                        <div class="card-body">
                            <i class="fas fa-user-md fa-3x text-success mb-3"></i>
                            <h5>ورشات للأخصائيين</h5>
                            <p>تطوير المهارات المهنية وأحدث الطرق العلاجية</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card text-center h-100">
                        <div class="card-body">
                            <i class="fas fa-child fa-3x text-warning mb-3"></i>
                            <h5>ورشات للأطفال</h5>
                            <p>أنشطة تفاعلية ممتعة لتطوير مهارات الأطفال</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card text-center h-100">
                        <div class="card-body">
                            <i class="fas fa-graduation-cap fa-3x text-info mb-3"></i>
                            <h5>ورشات تخصصية</h5>
                            <p>موضوعات متقدمة في علاج وتأهيل التوحد</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- الورشات السابقة -->
    <?php if (!empty($past_workshops)): ?>
    <section class="section">
        <div class="container">
            <h2 class="section-title">ورشات سابقة للمراجعة</h2>
            <div class="row">
                <?php foreach ($past_workshops as $workshop): ?>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card workshop-card past-workshop">
                            <div class="workshop-header" style="background: linear-gradient(135deg, #9e9e9e, #757575);">
                                <div class="workshop-category">ورشة منتهية</div>
                                <div class="workshop-date">
                                    <i class="fas fa-calendar"></i>
                                    <?php echo date('Y/m/d', strtotime($workshop['date'])); ?>
                                </div>
                                <h6><?php echo htmlspecialchars($workshop['title']); ?></h6>
                            </div>
                            
                            <div class="card-body">
                                <p class="card-text small"><?php echo substr(htmlspecialchars($workshop['description']), 0, 100) . '...'; ?></p>
                                
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="instructor-badge" style="background: #9e9e9e;">
                                        <i class="fas fa-chalkboard-teacher"></i>
                                        <?php echo htmlspecialchars($workshop['instructor_name']); ?>
                                    </span>
                                    <button class="btn btn-outline-secondary btn-sm" onclick="viewWorkshopMaterials(<?php echo $workshop['id']; ?>)">
                                        <i class="fas fa-eye"></i> عرض المواد
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- مودال التسجيل -->
    <div class="modal fade" id="registrationModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content modal-arabic">
                <div class="modal-header">
                    <h5 class="modal-title">تسجيل في الورشة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        للتسجيل في الورشات، يرجى تسجيل الدخول أولاً أو إنشاء حساب جديد.
                    </div>
                    <div class="text-center">
                        <a href="login.php" class="btn btn-primary me-2">تسجيل الدخول</a>
                        <a href="register.php" class="btn btn-success">إنشاء حساب</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- التذييل -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div>
                    <h4>منصة نبراس</h4>
                    <p>منصة تعليمية متخصصة في دعم الأطفال ذوي اضطراب طيف التوحد وأسرهم</p>
                </div>
                <div>
                    <h4>روابط سريعة</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li><a href="index.php" style="color: #adb5bd; text-decoration: none;">الرئيسية</a></li>
                        <li><a href="about.php" style="color: #adb5bd; text-decoration: none;">من نحن</a></li>
                        <li><a href="library.php" style="color: #adb5bd; text-decoration: none;">المكتبة</a></li>
                        <li><a href="contact.php" style="color: #adb5bd; text-decoration: none;">اتصل بنا</a></li>
                    </ul>
                </div>
                <div>
                    <h4>تواصل معنا</h4>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +216 XX XXX XXX</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 منصة نبراس. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function registerWorkshop(workshopId) {
            // التحقق من تسجيل الدخول
            <?php if (is_logged_in()): ?>
                // المستخدم مسجل الدخول - يمكن التسجيل
                if (confirm('هل تريد التسجيل في هذه الورشة؟')) {
                    // يمكن إضافة كود التسجيل هنا
                    alert('تم التسجيل بنجاح! سيتم التواصل معك قريباً.');
                }
            <?php else: ?>
                // المستخدم غير مسجل الدخول
                const modal = new bootstrap.Modal(document.getElementById('registrationModal'));
                modal.show();
            <?php endif; ?>
        }
        
        function viewWorkshopMaterials(workshopId) {
            alert('سيتم إتاحة مواد الورشة قريباً للمراجعة.');
        }
        
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const workshopCards = document.querySelectorAll('.workshop-card');
            
            workshopCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>
</body>
</html>

<?php
require_once 'config.php';
require_once 'db.php';

// إعداد متغيرات الصفحة
$page_title = 'الورشات التفاعلية';
$page_description = 'ورشات تدريبية متخصصة في التوحد للأولياء والأخصائيين';

// جلب الورشات المتاحة
$workshops_query = "SELECT w.*, u.name as instructor_name FROM workshops w
                    JOIN users u ON w.instructor_id = u.id
                    WHERE w.is_active = 1 AND w.date >= NOW()
                    ORDER BY w.date ASC";
$workshops_stmt = $db->prepare($workshops_query);
$workshops_stmt->execute();
$workshops = $workshops_stmt->fetchAll();

// جلب الورشات السابقة (للمراجع)
$past_workshops_query = "SELECT w.*, u.name as instructor_name FROM workshops w
                         JOIN users u ON w.instructor_id = u.id
                         WHERE w.is_active = 1 AND w.date < NOW()
                         ORDER BY w.date DESC LIMIT 6";
$past_workshops_stmt = $db->prepare($past_workshops_query);
$past_workshops_stmt->execute();
$past_workshops = $past_workshops_stmt->fetchAll();

// إضافة CSS خاص بالصفحة
$additional_css = [
    get_asset_url('css/workshops.css')
];

// تضمين الهيدر
include 'includes/header.php';
?>

    <style>
        .workshops-header {
            background: linear-gradient(135deg, #ff9800, #f57c00, #e65100, #ffb74d);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
            color: white !important;
            padding: 3rem 0;
            border-radius: 0 0 30px 30px;
        }

        .workshops-header h1,
        .workshops-header p,
        .workshops-header .lead {
            color: white !important;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .floating-shapes {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            overflow: hidden;
            pointer-events: none;
        }

        .shape {
            position: absolute;
            color: rgba(255,255,255,0.1);
            animation: float 6s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            top: 60%;
            right: 15%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            bottom: 30%;
            left: 20%;
            animation-delay: 4s;
        }

        .shape:nth-child(4) {
            top: 40%;
            right: 30%;
            animation-delay: 6s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>

    <!-- قسم العنوان المحسن -->
    <section class="workshops-header position-relative">
        <div class="floating-shapes">
            <div class="shape"><i class="fas fa-chalkboard-teacher fa-3x"></i></div>
            <div class="shape"><i class="fas fa-users fa-3x"></i></div>
            <div class="shape"><i class="fas fa-graduation-cap fa-3x"></i></div>
            <div class="shape"><i class="fas fa-certificate fa-3x"></i></div>
        </div>
        <div class="container">
            <div class="hero-content text-center">
                <h1><i class="fas fa-chalkboard-teacher"></i> الورشات التفاعلية</h1>
                <p class="lead">ورشات تدريبية متخصصة لتطوير مهاراتك في التعامل مع التوحد</p>
                <div class="mt-4">
                    <span class="badge bg-light text-dark fs-6 me-2">
                        <i class="fas fa-users"></i> للأولياء
                    </span>
                    <span class="badge bg-light text-dark fs-6 me-2">
                        <i class="fas fa-user-md"></i> للأخصائيين
                    </span>
                    <span class="badge bg-light text-dark fs-6 me-2">
                        <i class="fas fa-child"></i> للأطفال
                    </span>
                    <span class="badge bg-light text-dark fs-6">
                        <i class="fas fa-certificate"></i> شهادات
                    </span>
                </div>
            </div>
        </div>
    </section>

    <!-- الورشات القادمة -->
    <section class="section">
        <div class="container">
            <h2 class="section-title">الورشات القادمة</h2>
            <?php if (!empty($workshops)): ?>
                <div class="row">
                    <?php foreach ($workshops as $workshop): ?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card workshop-card">
                                <div class="workshop-header">
                                    <div class="workshop-category">ورشة تدريبية</div>
                                    <div class="workshop-date">
                                        <i class="fas fa-calendar"></i>
                                        <?php echo date('Y/m/d', strtotime($workshop['date'])); ?>
                                        <br>
                                        <i class="fas fa-clock"></i>
                                        <?php echo date('H:i', strtotime($workshop['date'])); ?>
                                    </div>
                                    <h5><?php echo htmlspecialchars($workshop['title']); ?></h5>
                                </div>
                                
                                <div class="card-body">
                                    <p class="card-text"><?php echo htmlspecialchars($workshop['description']); ?></p>
                                    
                                    <div class="participants-info">
                                        <div class="row text-center">
                                            <div class="col-4">
                                                <i class="fas fa-clock text-primary"></i>
                                                <div><strong><?php echo $workshop['duration']; ?></strong></div>
                                                <small>دقيقة</small>
                                            </div>
                                            <div class="col-4">
                                                <i class="fas fa-users text-success"></i>
                                                <div><strong><?php echo $workshop['max_participants']; ?></strong></div>
                                                <small>مشارك</small>
                                            </div>
                                            <div class="col-4">
                                                <i class="fas fa-user-tie text-warning"></i>
                                                <div><strong>مجاني</strong></div>
                                                <small>التكلفة</small>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="instructor-badge">
                                            <i class="fas fa-chalkboard-teacher"></i>
                                            <?php echo htmlspecialchars($workshop['instructor_name']); ?>
                                        </span>
                                        <button class="btn register-btn" onclick="registerWorkshop(<?php echo $workshop['id']; ?>)">
                                            <i class="fas fa-user-plus"></i> سجل الآن
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle fa-3x mb-3"></i>
                    <h4>لا توجد ورشات متاحة حالياً</h4>
                    <p>سيتم الإعلان عن الورشات الجديدة قريباً. تابعنا للحصول على آخر التحديثات.</p>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- أنواع الورشات -->
    <section class="section" style="background-color: #f8f9fa;">
        <div class="container">
            <h2 class="section-title">أنواع الورشات المتاحة</h2>
            <div class="row">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card text-center h-100">
                        <div class="card-body">
                            <i class="fas fa-users fa-3x text-primary mb-3"></i>
                            <h5>ورشات للأولياء</h5>
                            <p>تعلم كيفية التعامل مع طفلك وتطوير مهاراته اليومية</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card text-center h-100">
                        <div class="card-body">
                            <i class="fas fa-user-md fa-3x text-success mb-3"></i>
                            <h5>ورشات للأخصائيين</h5>
                            <p>تطوير المهارات المهنية وأحدث الطرق العلاجية</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card text-center h-100">
                        <div class="card-body">
                            <i class="fas fa-child fa-3x text-warning mb-3"></i>
                            <h5>ورشات للأطفال</h5>
                            <p>أنشطة تفاعلية ممتعة لتطوير مهارات الأطفال</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card text-center h-100">
                        <div class="card-body">
                            <i class="fas fa-graduation-cap fa-3x text-info mb-3"></i>
                            <h5>ورشات تخصصية</h5>
                            <p>موضوعات متقدمة في علاج وتأهيل التوحد</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- الورشات السابقة -->
    <?php if (!empty($past_workshops)): ?>
    <section class="section">
        <div class="container">
            <h2 class="section-title">ورشات سابقة للمراجعة</h2>
            <div class="row">
                <?php foreach ($past_workshops as $workshop): ?>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card workshop-card past-workshop">
                            <div class="workshop-header" style="background: linear-gradient(135deg, #9e9e9e, #757575);">
                                <div class="workshop-category">ورشة منتهية</div>
                                <div class="workshop-date">
                                    <i class="fas fa-calendar"></i>
                                    <?php echo date('Y/m/d', strtotime($workshop['date'])); ?>
                                </div>
                                <h6><?php echo htmlspecialchars($workshop['title']); ?></h6>
                            </div>
                            
                            <div class="card-body">
                                <p class="card-text small"><?php echo substr(htmlspecialchars($workshop['description']), 0, 100) . '...'; ?></p>
                                
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="instructor-badge" style="background: #9e9e9e;">
                                        <i class="fas fa-chalkboard-teacher"></i>
                                        <?php echo htmlspecialchars($workshop['instructor_name']); ?>
                                    </span>
                                    <button class="btn btn-outline-secondary btn-sm" onclick="viewWorkshopMaterials(<?php echo $workshop['id']; ?>)">
                                        <i class="fas fa-eye"></i> عرض المواد
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- مودال التسجيل -->
    <div class="modal fade" id="registrationModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content modal-arabic">
                <div class="modal-header">
                    <h5 class="modal-title">تسجيل في الورشة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        للتسجيل في الورشات، يرجى تسجيل الدخول أولاً أو إنشاء حساب جديد.
                    </div>
                    <div class="text-center">
                        <a href="login.php" class="btn btn-primary me-2">تسجيل الدخول</a>
                        <a href="register.php" class="btn btn-success">إنشاء حساب</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

<?php
// إضافة JavaScript خاص بالصفحة
$page_js = "
    // تهيئة دوال الورشات
    function registerWorkshop(workshopId) {
        PageSpecific.workshops.registerWorkshop(workshopId);
    }

    function viewWorkshopMaterials(workshopId) {
        NibrassHelpers.showAlert('سيتم إتاحة مواد الورشة قريباً للمراجعة.', 'info');
    }
";

// تضمين الفوتر
include 'includes/footer.php';
?>

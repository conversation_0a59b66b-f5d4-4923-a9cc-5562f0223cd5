<?php
require_once 'config.php';
require_once 'db.php';

// التحقق من رسالة تسجيل الخروج
$logout_message = '';
if (isset($_GET['logged_out']) && $_GET['logged_out'] == '1') {
    $logout_message = 'تم تسجيل الخروج بنجاح. نراك قريباً!';
}

// جلب الأخبار من قاعدة البيانات
$news_query = "SELECT * FROM news WHERE is_active = 1 ORDER BY created_at DESC LIMIT 5";
$news_stmt = $db->prepare($news_query);
$news_stmt->execute();
$news_items = $news_stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SITE_NAME; ?> - الصفحة الرئيسية</title>
    <meta name="description" content="<?php echo SITE_DESCRIPTION; ?>">
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="style.css" rel="stylesheet">
    <!-- RTL Enhancements -->
    <link href="rtl-enhancements.css" rel="stylesheet">
</head>
<body>
    <!-- الشريط العلوي المحسن -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top enhanced-navbar">
        <div class="container">
            <a class="navbar-brand logo-enhanced" href="index.php">
                <i class="fas fa-star logo-icon"></i>
                <span class="logo-text">نبراس</span>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">
                            <i class="fas fa-home nav-icon"></i>
                            <span>الرئيسية</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">
                            <i class="fas fa-info-circle nav-icon"></i>
                            <span>من نحن</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="library.php">
                            <i class="fas fa-book nav-icon"></i>
                            <span>المكتبة</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="workshops.php">
                            <i class="fas fa-chalkboard-teacher nav-icon"></i>
                            <span>الورشات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="creativity.php">
                            <i class="fas fa-palette nav-icon"></i>
                            <span>الإبداع</span>
                        </a>
                    </li>
                    <?php if (is_logged_in()): ?>
                        <li class="nav-item">
                            <a class="nav-link btn-ai-chat" href="ai-chat.php">
                                <i class="fas fa-robot nav-icon"></i>
                                <span>المساعد الذكي</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link btn-logout" href="logout.php">
                                <i class="fas fa-sign-out-alt nav-icon"></i>
                                <span>تسجيل الخروج</span>
                            </a>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="contact.php">
                                <i class="fas fa-envelope nav-icon"></i>
                                <span>اتصل بنا</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link btn-login" href="login.php">
                                <i class="fas fa-sign-in-alt nav-icon"></i>
                                <span>تسجيل الدخول</span>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- شريط الأخبار المحسن - Slider -->
    <?php /* if (!empty($news_items)): ?>
    <div class="news-slider-container">
        <div id="newsCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="4000">
            <div class="carousel-inner">
                <?php foreach ($news_items as $index => $news): ?>
                    <div class="carousel-item <?php echo $index === 0 ? 'active' : ''; ?>">
                        <div class="news-slide">
                            <i class="fas fa-bullhorn news-icon"></i>
                            <span class="news-text"><?php echo htmlspecialchars($news['title']); ?></span>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            <button class="carousel-control-prev" type="button" data-bs-target="#newsCarousel" data-bs-slide="prev">
                <i class="fas fa-chevron-right"></i>
            </button>
            <button class="carousel-control-next" type="button" data-bs-target="#newsCarousel" data-bs-slide="next">
                <i class="fas fa-chevron-left"></i>
            </button>
        </div>
    </div>
    <?php endif; */ ?>

    <!-- رسالة تسجيل الخروج -->
    <?php if (!empty($logout_message)): ?>
    <div class="container mt-3">
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i>
            <?php echo $logout_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    </div>
    <?php endif; ?>

    <!-- القسم الرئيسي المحسن -->
    <section class="hero-section enhanced-hero">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-7">
                    <div class="hero-content">
                        <h1 class="hero-title animated-text">
                            <span class="typewriter">مرحباً بكم في منصة نبراس</span>
                        </h1>
                        <p class="hero-description fade-in-up">
                            منصة تعليمية متخصصة في دعم الأطفال ذوي اضطراب طيف التوحد وأسرهم
                        </p>
                        <div class="hero-buttons fade-in-up">
                            <a href="register.php" class="btn btn-primary btn-enhanced">
                                <i class="fas fa-user-plus"></i>
                                انضم إلينا الآن
                            </a>
                            <a href="about.php" class="btn btn-secondary btn-enhanced">
                                <i class="fas fa-info-circle"></i>
                                تعرف علينا أكثر
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-5">
                    <div class="hero-image fade-in-right">
                        <div class="hero-image-container">
                            <img src="images/Hero.svg"
                                 alt="منصة نبراس" class="img-fluid hero-img">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم قصتنا -->
    <section class="section">
        <div class="container">
            <h2 class="section-title">قصتنا</h2>
            <div class="grid grid-2">
                <div class="card">
                    <div class="card-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h3>رسالتنا</h3>
                    <p>نسعى لتوفير بيئة تعليمية داعمة ومحفزة للأطفال ذوي اضطراب طيف التوحد، مع تقديم الدعم والإرشاد لأسرهم والمختصين العاملين معهم.</p>
                </div>
                <div class="card">
                    <div class="card-icon">
                        <i class="fas fa-eye"></i>
                    </div>
                    <h3>رؤيتنا</h3>
                    <p>أن نكون المنصة الرائدة في المنطقة العربية لدعم الأطفال ذوي اضطراب طيف التوحد وتمكينهم من تحقيق أقصى إمكاناتهم.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- أهداف المنصة - تخطيط 3x3 محسن -->
    <section class="section goals-section" style="background-color: #f8f9fa;">
        <div class="container">
            <h2 class="section-title scroll-animate">أهداف منصة نبراس</h2>
            <div class="goals-grid">
                <div class="card goal-card scroll-animate" data-delay="100">
                    <div class="card-body text-center">
                        <div class="card-icon">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <h3>التعليم المتخصص</h3>
                        <p>توفير محتوى تعليمي مصمم خصيصاً لاحتياجات الأطفال ذوي اضطراب طيف التوحد</p>
                    </div>
                </div>
                <div class="card goal-card scroll-animate" data-delay="200">
                    <div class="card-body text-center">
                        <div class="card-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3>دعم الأسر</h3>
                        <p>تقديم الاستشارات والإرشاد للأولياء لمساعدتهم في رحلة تربية أطفالهم</p>
                    </div>
                </div>
                <div class="card goal-card scroll-animate" data-delay="300">
                    <div class="card-body text-center">
                        <div class="card-icon">
                            <i class="fas fa-brain"></i>
                        </div>
                        <h3>تطوير المهارات</h3>
                        <p>برامج متنوعة لتنمية المهارات الاجتماعية والتواصلية والأكاديمية</p>
                    </div>
                </div>
                <div class="card goal-card scroll-animate" data-delay="400">
                    <div class="card-body text-center">
                        <div class="card-icon">
                            <i class="fas fa-book"></i>
                        </div>
                        <h3>المكتبة الرقمية</h3>
                        <p>مجموعة شاملة من الكتب والمقالات والموارد التعليمية المتخصصة</p>
                    </div>
                </div>
                <div class="card goal-card scroll-animate" data-delay="500">
                    <div class="card-body text-center">
                        <div class="card-icon">
                            <i class="fas fa-microphone"></i>
                        </div>
                        <h3>البودكاست التعليمي</h3>
                        <p>محتوى صوتي متنوع يغطي مختلف جوانب التوحد والتعامل معه</p>
                    </div>
                </div>
                <div class="card goal-card scroll-animate" data-delay="600">
                    <div class="card-body text-center">
                        <div class="card-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <h3>الذكاء الاصطناعي</h3>
                        <p>مساعد ذكي لتقديم الإجابات والدعم الفوري للأولياء والمختصين</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- فضاءات المنصة -->
    <section class="section spaces-section">
        <div class="container">
            <h2 class="section-title scroll-animate">فضاءات المنصة</h2>
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card space-card scroll-animate text-center" data-delay="100">
                        <div class="card-body">
                            <div class="card-icon">
                                <i class="fas fa-child"></i>
                            </div>
                            <h3>فضاء التلاميذ</h3>
                            <p>مساحة تفاعلية للأطفال تتضمن الألعاب التعليمية والأنشطة المحفزة</p>
                            <a href="student-space/dashboard.php" class="btn btn-primary mt-3">
                                <i class="fas fa-play"></i> دخول الفضاء
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card space-card scroll-animate text-center" data-delay="200">
                        <div class="card-body">
                            <div class="card-icon">
                                <i class="fas fa-user-friends"></i>
                            </div>
                            <h3>فضاء الأولياء</h3>
                            <p>منطقة مخصصة للأولياء للحصول على الاستشارات والدعم</p>
                            <a href="parent-space/dashboard.php" class="btn btn-primary mt-3">
                                <i class="fas fa-heart"></i> دخول الفضاء
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card space-card scroll-animate text-center" data-delay="300">
                        <div class="card-body">
                            <div class="card-icon">
                                <i class="fas fa-user-md"></i>
                            </div>
                            <h3>فضاء الأخصائيين</h3>
                            <p>منصة للمختصين لتبادل الخبرات والموارد التعليمية</p>
                            <a href="specialist-space/dashboard.php" class="btn btn-primary mt-3">
                                <i class="fas fa-stethoscope"></i> دخول الفضاء
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- التذييل المحسن -->
    <footer class="footer scroll-animate">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4><i class="fas fa-star"></i> منصة نبراس</h4>
                    <p>منصة تعليمية متخصصة في دعم الأطفال ذوي اضطراب طيف التوحد وأسرهم</p>
                    <div class="footer-stats">
                        <div class="stat-item">
                            <i class="fas fa-users"></i>
                            <span>500+ مستفيد</span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-book"></i>
                            <span>100+ مورد</span>
                        </div>
                    </div>
                </div>
                <div class="footer-section">
                    <h4><i class="fas fa-link"></i> روابط سريعة</h4>
                    <ul class="footer-links">
                        <li><a href="about.php"><i class="fas fa-info-circle"></i> من نحن</a></li>
                        <li><a href="library.php"><i class="fas fa-book"></i> المكتبة</a></li>
                        <li><a href="workshops.php"><i class="fas fa-chalkboard-teacher"></i> الورشات</a></li>
                        <li><a href="creativity.php"><i class="fas fa-palette"></i> الإبداع</a></li>
                        <li><a href="contact.php"><i class="fas fa-envelope"></i> اتصل بنا</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4><i class="fas fa-phone"></i> تواصل معنا</h4>
                    <div class="contact-info">
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-phone"></i> +213 XX XXX XXX</p>
                        <p><i class="fas fa-map-marker-alt"></i> الجزائر، الجزائر</p>
                    </div>
                    <div class="social-links">
                        <a href="#" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="social-link twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="social-link instagram">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="social-link youtube">
                            <i class="fab fa-youtube"></i>
                        </a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p>&copy; 2025 منصة نبراس. جميع الحقوق محفوظة.</p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <p>صُنع بـ <i class="fas fa-heart text-danger"></i> في الجزائر</p>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Enhanced JavaScript for Animations and Interactions -->
    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const delay = entry.target.dataset.delay || 0;
                    setTimeout(() => {
                        entry.target.classList.add('animate-in');
                    }, delay);
                }
            });
        }, observerOptions);

        // Observe all scroll-animate elements
        document.querySelectorAll('.scroll-animate').forEach(el => {
            observer.observe(el);
        });

        // Typewriter effect for hero title
        function typeWriter(element, text, speed = 100) {
            let i = 0;
            element.innerHTML = '';

            function type() {
                if (i < text.length) {
                    element.innerHTML += text.charAt(i);
                    i++;
                    setTimeout(type, speed);
                }
            }
            type();
        }

        // Initialize typewriter effect when page loads
        window.addEventListener('load', () => {
            const heroTitle = document.querySelector('.typewriter');
            if (heroTitle) {
                const text = heroTitle.textContent;
                typeWriter(heroTitle, text, 80);
            }
        });

        // Navbar scroll effect
        window.addEventListener('scroll', () => {
            const navbar = document.querySelector('.enhanced-navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Floating icons animation
        function animateFloatingIcons() {
            const icons = document.querySelectorAll('.floating-icon');
            icons.forEach((icon, index) => {
                icon.style.animationDelay = `${index * 0.5}s`;
            });
        }

        // Initialize floating icons animation
        document.addEventListener('DOMContentLoaded', animateFloatingIcons);

        // Enhanced card hover effects
        document.querySelectorAll('.goal-card, .space-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // News carousel auto-pause on hover
        const newsCarousel = document.getElementById('newsCarousel');
        if (newsCarousel) {
            newsCarousel.addEventListener('mouseenter', () => {
                bootstrap.Carousel.getInstance(newsCarousel).pause();
            });

            newsCarousel.addEventListener('mouseleave', () => {
                bootstrap.Carousel.getInstance(newsCarousel).cycle();
            });
        }
    </script>
</body>
</html>

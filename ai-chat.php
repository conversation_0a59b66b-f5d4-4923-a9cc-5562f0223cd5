<?php
require_once 'config.php';
require_once 'db.php';

// التحقق من تسجيل الدخول
if (!is_logged_in()) {
    redirect('login.php');
}

$user = current_user();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المساعد الذكي - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="style.css" rel="stylesheet">
    <link href="rtl-enhancements.css" rel="stylesheet">
    
    <style>
        .chat-container {
            height: 70vh;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .chat-header {
            background: linear-gradient(135deg, #4a90e2, #2c5aa0);
            color: white;
            padding: 1rem;
            text-align: center;
        }
        
        .chat-messages {
            height: calc(100% - 140px);
            overflow-y: auto;
            padding: 1rem;
            background: white;
        }
        
        .message {
            margin-bottom: 1rem;
            display: flex;
            align-items: flex-start;
        }
        
        .message.user {
            justify-content: flex-end;
        }
        
        .message.ai {
            justify-content: flex-start;
        }
        
        .message-content {
            max-width: 70%;
            padding: 1rem;
            border-radius: 20px;
            position: relative;
        }
        
        .message.user .message-content {
            background: linear-gradient(135deg, #4caf50, #8bc34a);
            color: white;
            border-bottom-left-radius: 5px;
        }
        
        .message.ai .message-content {
            background: #f1f3f4;
            color: #333;
            border-bottom-right-radius: 5px;
        }
        
        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 0.5rem;
            font-size: 1.2rem;
        }
        
        .message.user .message-avatar {
            background: linear-gradient(135deg, #4caf50, #8bc34a);
            color: white;
            order: 2;
        }
        
        .message.ai .message-avatar {
            background: linear-gradient(135deg, #ff9800, #ff5722);
            color: white;
        }
        
        .chat-input {
            padding: 1rem;
            background: white;
            border-top: 1px solid #e9ecef;
        }
        
        .typing-indicator {
            display: none;
            padding: 1rem;
            color: #666;
            font-style: italic;
        }
        
        .typing-dots {
            display: inline-block;
            animation: typing 1.5s infinite;
        }
        
        @keyframes typing {
            0%, 60%, 100% { opacity: 0; }
            30% { opacity: 1; }
        }
        
        .quick-questions {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .quick-question-btn {
            background: white;
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            border-radius: 20px;
            padding: 0.5rem 1rem;
            margin: 0.25rem;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        
        .quick-question-btn:hover {
            background: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>
    <!-- الشريط العلوي -->
    <nav class="navbar navbar-expand-lg" style="background-color: var(--nibrass-blue);">
        <div class="container">
            <a class="navbar-brand text-white" href="index.php">
                <i class="fas fa-star"></i> نبراس
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="javascript:history.back()">
                    <i class="fas fa-arrow-right"></i> العودة
                </a>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <section class="section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="chat-container">
                        <!-- رأس الشات -->
                        <div class="chat-header">
                            <h4><i class="fas fa-robot"></i> المساعد الذكي لنبراس</h4>
                            <p class="mb-0">مرحباً <?php echo htmlspecialchars($user['name']); ?>! أنا هنا لمساعدتك في أي أسئلة حول التوحد</p>
                        </div>
                        
                        <!-- الرسائل -->
                        <div class="chat-messages" id="chatMessages">
                            <!-- رسالة ترحيب -->
                            <div class="message ai">
                                <div class="message-avatar">
                                    <i class="fas fa-robot"></i>
                                </div>
                                <div class="message-content">
                                    <p>مرحباً بك! أنا المساعد الذكي لمنصة نبراس. يمكنني مساعدتك في:</p>
                                    <ul class="mb-0">
                                        <li>الإجابة على أسئلة حول اضطراب طيف التوحد</li>
                                        <li>تقديم نصائح للتعامل مع الأطفال</li>
                                        <li>اقتراح أنشطة تعليمية مناسبة</li>
                                        <li>معلومات عن الخدمات المتاحة في المنصة</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <!-- مؤشر الكتابة -->
                        <div class="typing-indicator" id="typingIndicator">
                            <div class="message ai">
                                <div class="message-avatar">
                                    <i class="fas fa-robot"></i>
                                </div>
                                <div class="message-content">
                                    المساعد يكتب<span class="typing-dots">...</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- منطقة الإدخال -->
                        <div class="chat-input">
                            <div class="input-group">
                                <input type="text" class="form-control" id="messageInput" 
                                       placeholder="اكتب سؤالك هنا..." 
                                       onkeypress="handleKeyPress(event)">
                                <button class="btn btn-primary" onclick="sendMessage()">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- الأسئلة السريعة -->
                    <div class="quick-questions mt-4">
                        <h6><i class="fas fa-lightbulb"></i> أسئلة شائعة:</h6>
                        <button class="btn quick-question-btn" onclick="askQuickQuestion('ما هو اضطراب طيف التوحد؟')">
                            ما هو اضطراب طيف التوحد؟
                        </button>
                        <button class="btn quick-question-btn" onclick="askQuickQuestion('كيف أتعامل مع نوبات الغضب؟')">
                            كيف أتعامل مع نوبات الغضب؟
                        </button>
                        <button class="btn quick-question-btn" onclick="askQuickQuestion('ما هي أفضل الأنشطة التعليمية؟')">
                            ما هي أفضل الأنشطة التعليمية؟
                        </button>
                        <button class="btn quick-question-btn" onclick="askQuickQuestion('كيف أطور مهارات التواصل؟')">
                            كيف أطور مهارات التواصل؟
                        </button>
                        <button class="btn quick-question-btn" onclick="askQuickQuestion('ما هي علامات التوحد المبكرة؟')">
                            ما هي علامات التوحد المبكرة؟
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        const chatMessages = document.getElementById('chatMessages');
        const messageInput = document.getElementById('messageInput');
        const typingIndicator = document.getElementById('typingIndicator');
        
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        function askQuickQuestion(question) {
            messageInput.value = question;
            sendMessage();
        }
        
        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;
            
            // إضافة رسالة المستخدم
            addMessage(message, 'user');
            messageInput.value = '';
            
            // إظهار مؤشر الكتابة
            showTypingIndicator();
            
            try {
                // إرسال الرسالة إلى API
                const response = await fetch('api/chat.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        user_id: <?php echo $user['id']; ?>
                    })
                });
                
                const data = await response.json();
                
                // إخفاء مؤشر الكتابة
                hideTypingIndicator();
                
                if (data.success) {
                    // إضافة رد المساعد الذكي
                    addMessage(data.response, 'ai');
                } else {
                    addMessage('عذراً، حدث خطأ في النظام. يرجى المحاولة لاحقاً.', 'ai');
                }
            } catch (error) {
                hideTypingIndicator();
                addMessage('عذراً، لا يمكنني الاتصال بالخادم حالياً. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.', 'ai');
            }
        }
        
        function addMessage(content, sender) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            const avatar = sender === 'user' ? 
                '<i class="fas fa-user"></i>' : 
                '<i class="fas fa-robot"></i>';
            
            messageDiv.innerHTML = `
                <div class="message-avatar">
                    ${avatar}
                </div>
                <div class="message-content">
                    <p class="mb-0">${content}</p>
                </div>
            `;
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        function showTypingIndicator() {
            typingIndicator.style.display = 'block';
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        function hideTypingIndicator() {
            typingIndicator.style.display = 'none';
        }
        
        // تركيز على حقل الإدخال عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            messageInput.focus();
        });
    </script>
</body>
</html>

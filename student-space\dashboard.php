<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كطالب
require_student();

$user = current_user();
$student_info = get_student_info($user['id']);

if (!$student_info) {
    redirect('../index.php');
}

// جلب الأنشطة والألعاب المتاحة
$activities_query = "SELECT * FROM media WHERE category IN ('ألعاب تعليمية', 'أنشطة تفاعلية') AND is_public = 1 ORDER BY created_at DESC LIMIT 6";
$activities_stmt = $db->prepare($activities_query);
$activities_stmt->execute();
$activities = $activities_stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فضاء التلاميذ - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../style.css" rel="stylesheet">
    <link href="../rtl-enhancements.css" rel="stylesheet">
    
    <style>
        .student-header {
            background: linear-gradient(135deg, #ff9800, #4caf50);
            color: white;
            padding: 2rem 0;
            border-radius: 0 0 20px 20px;
        }
        
        .star-counter {
            background: rgba(255,255,255,0.2);
            border-radius: 15px;
            padding: 1rem;
            text-align: center;
        }
        
        .activity-card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            cursor: pointer;
        }
        
        .activity-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .progress-ring {
            width: 120px;
            height: 120px;
            margin: 0 auto;
        }
        
        .game-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <!-- الشريط العلوي -->
    <nav class="navbar navbar-expand-lg" style="background-color: var(--nibrass-blue);">
        <div class="container">
            <a class="navbar-brand text-white" href="../index.php">
                <i class="fas fa-star"></i> نبراس
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> <?php echo htmlspecialchars($user['name']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user"></i> الملف الشخصي</a></li>
                        <li><a class="dropdown-item" href="achievements.php"><i class="fas fa-trophy"></i> الإنجازات</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- رأس فضاء التلميذ -->
    <section class="student-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-child"></i> مرحباً <?php echo htmlspecialchars($student_info['name']); ?>!</h1>
                    <p class="lead">استعد لرحلة تعلم ممتعة مليئة بالألعاب والأنشطة الشيقة</p>
                </div>
                <div class="col-md-4">
                    <div class="row">
                        <div class="col-6">
                            <div class="star-counter">
                                <i class="fas fa-star fa-2x text-warning"></i>
                                <h3><?php echo $student_info['weekly_stars']; ?></h3>
                                <small>نجوم هذا الأسبوع</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="star-counter">
                                <i class="fas fa-trophy fa-2x text-warning"></i>
                                <h3><?php echo $student_info['total_stars']; ?></h3>
                                <small>مجموع النجوم</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم التقدم -->
    <section class="section">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5><i class="fas fa-chart-line text-primary"></i> مستوى التقدم</h5>
                            <div class="progress-ring">
                                <svg width="120" height="120">
                                    <circle cx="60" cy="60" r="50" stroke="#e9ecef" stroke-width="8" fill="none"/>
                                    <circle cx="60" cy="60" r="50" stroke="#4caf50" stroke-width="8" fill="none"
                                            stroke-dasharray="314" stroke-dashoffset="<?php echo 314 - (314 * $student_info['progress_level'] / 100); ?>"
                                            stroke-linecap="round" transform="rotate(-90 60 60)"/>
                                    <text x="60" y="65" text-anchor="middle" font-size="20" font-weight="bold" fill="#4caf50">
                                        <?php echo $student_info['progress_level']; ?>%
                                    </text>
                                </svg>
                            </div>
                            <p class="mt-2">مستوى ممتاز! استمر في التقدم</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 mb-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5><i class="fas fa-calendar-check text-success"></i> الأنشطة اليومية</h5>
                            <div class="mt-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>قراءة القصة</span>
                                    <i class="fas fa-check-circle text-success"></i>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>لعبة الألوان</span>
                                    <i class="fas fa-check-circle text-success"></i>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>تمرين الأرقام</span>
                                    <i class="fas fa-clock text-warning"></i>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>نشاط الرسم</span>
                                    <i class="fas fa-circle text-muted"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 mb-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5><i class="fas fa-award text-warning"></i> إنجاز الأسبوع</h5>
                            <div class="mt-3">
                                <i class="fas fa-medal fa-3x text-warning mb-3"></i>
                                <h6>نجم الأسبوع في الرياضيات!</h6>
                                <p class="small text-muted">حصلت على 15 نجمة في تمارين الأرقام</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- الألعاب والأنشطة -->
    <section class="section" style="background-color: #f8f9fa;">
        <div class="container">
            <h2 class="section-title">الألعاب والأنشطة</h2>
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card activity-card" onclick="startActivity('colors')">
                        <div class="card-body text-center">
                            <div class="game-icon" style="color: #ff5722;">
                                <i class="fas fa-palette"></i>
                            </div>
                            <h5>لعبة الألوان</h5>
                            <p>تعلم الألوان بطريقة ممتعة وتفاعلية</p>
                            <span class="badge bg-success">سهل</span>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card activity-card" onclick="startActivity('numbers')">
                        <div class="card-body text-center">
                            <div class="game-icon" style="color: #2196f3;">
                                <i class="fas fa-calculator"></i>
                            </div>
                            <h5>مغامرة الأرقام</h5>
                            <p>اكتشف عالم الأرقام والحساب</p>
                            <span class="badge bg-warning">متوسط</span>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card activity-card" onclick="startActivity('letters')">
                        <div class="card-body text-center">
                            <div class="game-icon" style="color: #4caf50;">
                                <i class="fas fa-font"></i>
                            </div>
                            <h5>حروفي الجميلة</h5>
                            <p>تعلم الحروف العربية بالصوت والصورة</p>
                            <span class="badge bg-success">سهل</span>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card activity-card" onclick="startActivity('shapes')">
                        <div class="card-body text-center">
                            <div class="game-icon" style="color: #9c27b0;">
                                <i class="fas fa-shapes"></i>
                            </div>
                            <h5>الأشكال الهندسية</h5>
                            <p>اكتشف الأشكال من حولك</p>
                            <span class="badge bg-info">سهل</span>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card activity-card" onclick="startActivity('memory')">
                        <div class="card-body text-center">
                            <div class="game-icon" style="color: #ff9800;">
                                <i class="fas fa-brain"></i>
                            </div>
                            <h5>لعبة الذاكرة</h5>
                            <p>قوي ذاكرتك مع هذه اللعبة الشيقة</p>
                            <span class="badge bg-warning">متوسط</span>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card activity-card" onclick="startActivity('puzzle')">
                        <div class="card-body text-center">
                            <div class="game-icon" style="color: #607d8b;">
                                <i class="fas fa-puzzle-piece"></i>
                            </div>
                            <h5>الألغاز المصورة</h5>
                            <p>حل الألغاز واكتشف الصور</p>
                            <span class="badge bg-secondary">متقدم</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- القصص التفاعلية -->
    <section class="section">
        <div class="container">
            <h2 class="section-title">القصص التفاعلية</h2>
            <div class="row">
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="fas fa-book-open fa-3x text-primary"></i>
                                </div>
                                <div>
                                    <h5>قصة أحمد والألوان</h5>
                                    <p class="mb-2">قصة ممتعة عن اكتشاف الألوان</p>
                                    <button class="btn btn-primary btn-sm" onclick="readStory('colors-story')">
                                        <i class="fas fa-play"></i> اقرأ القصة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="fas fa-book-open fa-3x text-success"></i>
                                </div>
                                <div>
                                    <h5>مغامرة في عالم الأرقام</h5>
                                    <p class="mb-2">رحلة شيقة لتعلم الأرقام</p>
                                    <button class="btn btn-success btn-sm" onclick="readStory('numbers-story')">
                                        <i class="fas fa-play"></i> اقرأ القصة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function startActivity(activityType) {
            // يمكن تطوير هذه الوظائف لاحقاً لتشغيل الألعاب الفعلية
            switch(activityType) {
                case 'colors':
                    alert('🎨 مرحباً بك في لعبة الألوان! سنبدأ قريباً...');
                    break;
                case 'numbers':
                    alert('🔢 استعد لمغامرة الأرقام الشيقة!');
                    break;
                case 'letters':
                    alert('📝 هيا نتعلم الحروف العربية الجميلة!');
                    break;
                case 'shapes':
                    alert('🔷 اكتشف الأشكال الهندسية معنا!');
                    break;
                case 'memory':
                    alert('🧠 اختبر قوة ذاكرتك!');
                    break;
                case 'puzzle':
                    alert('🧩 حل الألغاز واستمتع!');
                    break;
                default:
                    alert('نشاط ممتع ينتظرك!');
            }
        }
        
        function readStory(storyType) {
            alert('📖 القصة ستبدأ قريباً... استعد للمتعة والتعلم!');
        }
        
        // تأثير بصري للنجوم
        document.addEventListener('DOMContentLoaded', function() {
            const stars = document.querySelectorAll('.fa-star');
            stars.forEach(star => {
                star.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.2) rotate(15deg)';
                    this.style.transition = 'transform 0.3s ease';
                });
                
                star.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1) rotate(0deg)';
                });
            });
        });
    </script>
</body>
</html>

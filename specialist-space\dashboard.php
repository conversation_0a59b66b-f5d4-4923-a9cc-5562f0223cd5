<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كأخصائي
require_specialist();

$user = current_user();

// جلب الاستشارات المعلقة
$pending_consultations_query = "SELECT f.*, u.name as user_name FROM feedback f 
                                JOIN users u ON f.user_id = u.id 
                                WHERE f.status = 'pending' 
                                ORDER BY f.created_at ASC LIMIT 5";
$pending_stmt = $db->prepare($pending_consultations_query);
$pending_stmt->execute();
$pending_consultations = $pending_stmt->fetchAll();

// جلب الورشات التي يقدمها الأخصائي
$workshops_query = "SELECT * FROM workshops WHERE instructor_id = :instructor_id AND is_active = 1 ORDER BY date ASC";
$workshops_stmt = $db->prepare($workshops_query);
$workshops_stmt->bindParam(':instructor_id', $user['id']);
$workshops_stmt->execute();
$workshops = $workshops_stmt->fetchAll();

// جلب إحصائيات الأخصائي
$stats_query = "SELECT 
                    (SELECT COUNT(*) FROM feedback WHERE responded_by = :user_id) as answered_consultations,
                    (SELECT COUNT(*) FROM workshops WHERE instructor_id = :user_id) as total_workshops,
                    (SELECT COUNT(*) FROM media WHERE uploaded_by = :user_id) as uploaded_resources";
$stats_stmt = $db->prepare($stats_query);
$stats_stmt->bindParam(':user_id', $user['id']);
$stats_stmt->execute();
$stats = $stats_stmt->fetch();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فضاء الأخصائيين - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../style.css" rel="stylesheet">
    <link href="../rtl-enhancements.css" rel="stylesheet">
    
    <style>
        .specialist-header {
            background: linear-gradient(135deg, #7b68ee, #9c27b0);
            color: white;
            padding: 2rem 0;
            border-radius: 0 0 20px 20px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #4caf50, #8bc34a);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .consultation-item {
            border-left: 4px solid var(--warning-color);
            background: #fff8e1;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        
        .consultation-item:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transform: translateX(-3px);
        }
        
        .workshop-card {
            border: 2px solid var(--primary-color);
            border-radius: 15px;
            transition: all 0.3s ease;
        }
        
        .workshop-card:hover {
            border-color: var(--nibrass-green);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .quick-action {
            background: linear-gradient(135deg, #ff9800, #ff5722);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 1rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .quick-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 152, 0, 0.3);
            color: white;
        }
    </style>
</head>
<body>
    <!-- الشريط العلوي -->
    <nav class="navbar navbar-expand-lg" style="background-color: var(--nibrass-blue);">
        <div class="container">
            <a class="navbar-brand text-white" href="../index.php">
                <i class="fas fa-star"></i> نبراس
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-md"></i> <?php echo htmlspecialchars($user['name']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user"></i> الملف الشخصي</a></li>
                        <li><a class="dropdown-item" href="consultations.php"><i class="fas fa-comments"></i> الاستشارات</a></li>
                        <li><a class="dropdown-item" href="workshops.php"><i class="fas fa-chalkboard-teacher"></i> ورشاتي</a></li>
                        <li><a class="dropdown-item" href="resources.php"><i class="fas fa-upload"></i> رفع موارد</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- رأس فضاء الأخصائيين -->
    <section class="specialist-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-user-md"></i> مرحباً د. <?php echo htmlspecialchars($user['name']); ?></h1>
                    <p class="lead">شكراً لك على جهودك في دعم الأطفال وأسرهم</p>
                </div>
                <div class="col-md-4 text-center">
                    <div class="d-flex justify-content-center gap-3">
                        <div class="text-center">
                            <i class="fas fa-comments fa-2x mb-2"></i>
                            <h4><?php echo $stats['answered_consultations'] ?? 0; ?></h4>
                            <small>استشارة مجابة</small>
                        </div>
                        <div class="text-center">
                            <i class="fas fa-chalkboard-teacher fa-2x mb-2"></i>
                            <h4><?php echo $stats['total_workshops'] ?? 0; ?></h4>
                            <small>ورشة تدريبية</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- الإحصائيات -->
    <section class="section">
        <div class="container">
            <div class="row">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-card">
                        <i class="fas fa-question-circle fa-3x mb-3"></i>
                        <h3><?php echo count($pending_consultations); ?></h3>
                        <p>استشارات في الانتظار</p>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-card" style="background: linear-gradient(135deg, #2196f3, #03a9f4);">
                        <i class="fas fa-check-circle fa-3x mb-3"></i>
                        <h3><?php echo $stats['answered_consultations'] ?? 0; ?></h3>
                        <p>استشارات مجابة</p>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-card" style="background: linear-gradient(135deg, #ff9800, #ff5722);">
                        <i class="fas fa-chalkboard-teacher fa-3x mb-3"></i>
                        <h3><?php echo count($workshops); ?></h3>
                        <p>ورشات نشطة</p>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-card" style="background: linear-gradient(135deg, #9c27b0, #e91e63);">
                        <i class="fas fa-upload fa-3x mb-3"></i>
                        <h3><?php echo $stats['uploaded_resources'] ?? 0; ?></h3>
                        <p>موارد مرفوعة</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- الإجراءات السريعة -->
    <section class="section" style="background-color: #f8f9fa;">
        <div class="container">
            <h2 class="section-title">الإجراءات السريعة</h2>
            <div class="row text-center">
                <div class="col-lg-3 col-md-6 mb-4">
                    <button class="quick-action w-100" data-bs-toggle="modal" data-bs-target="#newWorkshopModal">
                        <i class="fas fa-plus-circle fa-2x mb-2 d-block"></i>
                        إضافة ورشة جديدة
                    </button>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <button class="quick-action w-100" onclick="location.href='resources.php'">
                        <i class="fas fa-cloud-upload-alt fa-2x mb-2 d-block"></i>
                        رفع مورد تعليمي
                    </button>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <button class="quick-action w-100" onclick="location.href='consultations.php'">
                        <i class="fas fa-reply fa-2x mb-2 d-block"></i>
                        الرد على الاستشارات
                    </button>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <button class="quick-action w-100" data-bs-toggle="modal" data-bs-target="#newArticleModal">
                        <i class="fas fa-pen-fancy fa-2x mb-2 d-block"></i>
                        كتابة مقال
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- الاستشارات المعلقة -->
    <section class="section">
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <h3><i class="fas fa-clock text-warning"></i> الاستشارات في الانتظار</h3>
                    <?php if (!empty($pending_consultations)): ?>
                        <?php foreach ($pending_consultations as $consultation): ?>
                            <div class="consultation-item">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6><i class="fas fa-user"></i> <?php echo htmlspecialchars($consultation['user_name']); ?></h6>
                                        <h5><?php echo htmlspecialchars($consultation['subject']); ?></h5>
                                        <p class="mb-2"><?php echo substr(htmlspecialchars($consultation['message']), 0, 150) . '...'; ?></p>
                                        <small class="text-muted">
                                            <i class="fas fa-calendar"></i> <?php echo date('Y/m/d H:i', strtotime($consultation['created_at'])); ?>
                                            <span class="badge bg-info ms-2"><?php echo htmlspecialchars($consultation['type']); ?></span>
                                        </small>
                                    </div>
                                    <div>
                                        <button class="btn btn-success btn-sm" onclick="replyToConsultation(<?php echo $consultation['id']; ?>)">
                                            <i class="fas fa-reply"></i> رد
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        <div class="text-center">
                            <a href="consultations.php" class="btn btn-outline-primary">عرض جميع الاستشارات</a>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i>
                            ممتاز! لا توجد استشارات معلقة في الوقت الحالي.
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="col-lg-4">
                    <h3><i class="fas fa-chalkboard-teacher text-primary"></i> ورشاتي القادمة</h3>
                    <?php if (!empty($workshops)): ?>
                        <?php foreach (array_slice($workshops, 0, 3) as $workshop): ?>
                            <div class="card workshop-card mb-3">
                                <div class="card-body">
                                    <h6><?php echo htmlspecialchars($workshop['title']); ?></h6>
                                    <p class="small text-muted"><?php echo substr(htmlspecialchars($workshop['description']), 0, 80) . '...'; ?></p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small>
                                            <i class="fas fa-calendar"></i> <?php echo date('Y/m/d', strtotime($workshop['date'])); ?>
                                        </small>
                                        <small>
                                            <i class="fas fa-users"></i> <?php echo $workshop['max_participants']; ?> مشارك
                                        </small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        <div class="text-center">
                            <a href="workshops.php" class="btn btn-outline-primary btn-sm">عرض جميع الورشات</a>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            لم تقم بإنشاء أي ورشات بعد. يمكنك إضافة ورشة جديدة من الأزرار أعلاه.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>

    <!-- مودال إضافة ورشة جديدة -->
    <div class="modal fade" id="newWorkshopModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content modal-arabic">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة ورشة تدريبية جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="add_workshop.php">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="workshop_title" class="form-label">عنوان الورشة</label>
                                    <input type="text" class="form-control" id="workshop_title" name="title" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="workshop_date" class="form-label">تاريخ ووقت الورشة</label>
                                    <input type="datetime-local" class="form-control" id="workshop_date" name="date" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="workshop_duration" class="form-label">مدة الورشة (بالدقائق)</label>
                                    <input type="number" class="form-control" id="workshop_duration" name="duration" min="30" max="300" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="workshop_participants" class="form-label">عدد المشاركين الأقصى</label>
                                    <input type="number" class="form-control" id="workshop_participants" name="max_participants" min="5" max="100" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="workshop_description" class="form-label">وصف الورشة</label>
                            <textarea class="form-control" id="workshop_description" name="description" rows="4" required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">إضافة الورشة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- مودال كتابة مقال جديد -->
    <div class="modal fade" id="newArticleModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content modal-arabic">
                <div class="modal-header">
                    <h5 class="modal-title">كتابة مقال جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="add_article.php">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="form-group mb-3">
                                    <label for="article_title" class="form-label">عنوان المقال</label>
                                    <input type="text" class="form-control" id="article_title" name="title" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="article_category" class="form-label">التصنيف</label>
                                    <select class="form-control" id="article_category" name="category" required>
                                        <option value="">اختر التصنيف</option>
                                        <option value="نصائح للأولياء">نصائح للأولياء</option>
                                        <option value="تطوير المهارات">تطوير المهارات</option>
                                        <option value="تعليمي">تعليمي</option>
                                        <option value="بحوث ودراسات">بحوث ودراسات</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="article_content" class="form-label">محتوى المقال</label>
                            <textarea class="form-control" id="article_content" name="content" rows="10" required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" name="save_draft" class="btn btn-warning">حفظ كمسودة</button>
                        <button type="submit" name="publish" class="btn btn-success">نشر المقال</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function replyToConsultation(consultationId) {
            // يمكن تطوير هذه الوظيفة لفتح نافذة الرد
            window.location.href = 'reply_consultation.php?id=' + consultationId;
        }
        
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px) scale(1.02)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>
</body>
</html>

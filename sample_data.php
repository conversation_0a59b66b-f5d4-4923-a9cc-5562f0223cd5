<?php
/**
 * ملف إدراج البيانات التجريبية
 * Sample Data Insertion File
 */

require_once 'config.php';
require_once 'db.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إدراج البيانات التجريبية - منصة نبراس</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>";
echo "<link href='style.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";

echo "<div class='container mt-5'>";
echo "<div class='row justify-content-center'>";
echo "<div class='col-md-10'>";
echo "<div class='card'>";
echo "<div class='card-body'>";
echo "<h2 class='text-center mb-4'>إدراج البيانات التجريبية</h2>";

try {
    // إدراج مستخدمين تجريبيين
    $users_data = [
        [
            'name' => 'أحمد محمد',
            'email' => '<EMAIL>',
            'password' => password_hash('123456', PASSWORD_DEFAULT),
            'role' => 'parent',
            'phone' => '+216 12 345 678'
        ],
        [
            'name' => 'فاطمة علي',
            'email' => '<EMAIL>',
            'password' => password_hash('123456', PASSWORD_DEFAULT),
            'role' => 'specialist',
            'phone' => '+216 98 765 432'
        ],
        [
            'name' => 'محمد أحمد',
            'email' => '<EMAIL>',
            'password' => password_hash('123456', PASSWORD_DEFAULT),
            'role' => 'student',
            'phone' => ''
        ]
    ];

    foreach ($users_data as $user) {
        $sql = "INSERT IGNORE INTO users (name, email, password, role, phone) VALUES (?, ?, ?, ?, ?)";
        $stmt = $db->prepare($sql);
        $stmt->execute([$user['name'], $user['email'], $user['password'], $user['role'], $user['phone']]);
    }
    echo "<div class='alert alert-success'>✅ تم إدراج المستخدمين التجريبيين</div>";

    // إدراج مقالات تجريبية
    $articles_data = [
        [
            'title' => 'فهم اضطراب طيف التوحد',
            'content' => 'اضطراب طيف التوحد هو حالة نمائية تؤثر على التواصل والتفاعل الاجتماعي. يتميز بأنماط سلوكية متكررة واهتمامات محدودة. من المهم فهم أن كل طفل مصاب بالتوحد فريد من نوعه وله نقاط قوة وتحديات مختلفة.',
            'author_id' => 1,
            'category' => 'تعليمي',
            'is_published' => 1
        ],
        [
            'title' => 'استراتيجيات التواصل مع الأطفال ذوي التوحد',
            'content' => 'التواصل الفعال مع الأطفال ذوي التوحد يتطلب صبراً وفهماً. استخدم لغة بسيطة وواضحة، وامنح الطفل وقتاً كافياً للاستجابة. الإشارات البصرية والروتين المنتظم يمكن أن يساعدا كثيراً في تحسين التواصل.',
            'author_id' => 1,
            'category' => 'نصائح',
            'is_published' => 1
        ],
        [
            'title' => 'أهمية اللعب في تطوير مهارات الأطفال',
            'content' => 'اللعب ليس مجرد متعة للأطفال، بل هو وسيلة مهمة للتعلم وتطوير المهارات. للأطفال ذوي التوحد، يمكن أن يساعد اللعب المنظم في تطوير المهارات الاجتماعية والحركية والمعرفية.',
            'author_id' => 1,
            'category' => 'تطوير المهارات',
            'is_published' => 1
        ]
    ];

    foreach ($articles_data as $article) {
        $sql = "INSERT IGNORE INTO articles (title, content, author_id, category, is_published) VALUES (?, ?, ?, ?, ?)";
        $stmt = $db->prepare($sql);
        $stmt->execute([$article['title'], $article['content'], $article['author_id'], $article['category'], $article['is_published']]);
    }
    echo "<div class='alert alert-success'>✅ تم إدراج المقالات التجريبية</div>";

    // إدراج أخبار إضافية
    $news_data = [
        'ورشة تدريبية جديدة حول التعامل مع السلوكيات التحدية - التسجيل مفتوح الآن',
        'إطلاق مكتبة الكتب الصوتية المتخصصة في التوحد',
        'مؤتمر نبراس السنوي للتوحد - 15 ديسمبر 2024',
        'تطبيق نبراس الجديد متاح الآن على متجر التطبيقات',
        'برنامج الدعم النفسي للأسر - جلسات مجانية كل أسبوع'
    ];

    foreach ($news_data as $news_title) {
        $sql = "INSERT IGNORE INTO news (title, content, is_active) VALUES (?, ?, 1)";
        $stmt = $db->prepare($sql);
        $stmt->execute([$news_title, $news_title]);
    }
    echo "<div class='alert alert-success'>✅ تم إدراج الأخبار التجريبية</div>";

    // إدراج ورشات تجريبية
    $workshops_data = [
        [
            'title' => 'ورشة التواصل البصري',
            'description' => 'تعلم كيفية تطوير مهارات التواصل البصري لدى الأطفال ذوي التوحد',
            'date' => '2024-12-20 10:00:00',
            'duration' => 120,
            'max_participants' => 20,
            'instructor_id' => 1
        ],
        [
            'title' => 'ورشة إدارة السلوك',
            'description' => 'استراتيجيات فعالة لإدارة السلوكيات التحدية',
            'date' => '2024-12-25 14:00:00',
            'duration' => 90,
            'max_participants' => 15,
            'instructor_id' => 1
        ],
        [
            'title' => 'ورشة الأنشطة الحسية',
            'description' => 'أنشطة حسية مفيدة لتهدئة وتحفيز الأطفال',
            'date' => '2024-12-30 16:00:00',
            'duration' => 60,
            'max_participants' => 25,
            'instructor_id' => 1
        ]
    ];

    foreach ($workshops_data as $workshop) {
        $sql = "INSERT IGNORE INTO workshops (title, description, date, duration, max_participants, instructor_id) VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $db->prepare($sql);
        $stmt->execute([$workshop['title'], $workshop['description'], $workshop['date'], $workshop['duration'], $workshop['max_participants'], $workshop['instructor_id']]);
    }
    echo "<div class='alert alert-success'>✅ تم إدراج الورشات التجريبية</div>";

    // إدراج وسائط تجريبية
    $media_data = [
        [
            'title' => 'دليل الأولياء للتوحد',
            'description' => 'دليل شامل للأولياء حول كيفية التعامل مع الطفل ذي التوحد',
            'file_path' => 'uploads/documents/autism_parents_guide.pdf',
            'file_type' => 'pdf',
            'category' => 'أدلة',
            'uploaded_by' => 1,
            'file_size' => 2048000
        ],
        [
            'title' => 'أنشودة الألوان',
            'description' => 'أنشودة تعليمية لتعلم الألوان بطريقة ممتعة',
            'file_path' => 'uploads/audio/colors_song.mp3',
            'file_type' => 'audio',
            'category' => 'أناشيد',
            'uploaded_by' => 1,
            'file_size' => 5120000
        ],
        [
            'title' => 'فيديو تعليمي: المهارات الاجتماعية',
            'description' => 'فيديو يوضح كيفية تطوير المهارات الاجتماعية',
            'file_path' => 'uploads/video/social_skills.mp4',
            'file_type' => 'video',
            'category' => 'فيديوهات تعليمية',
            'uploaded_by' => 1,
            'file_size' => 15360000
        ]
    ];

    foreach ($media_data as $media) {
        $sql = "INSERT IGNORE INTO media (title, description, file_path, file_type, category, uploaded_by, file_size) VALUES (?, ?, ?, ?, ?, ?, ?)";
        $stmt = $db->prepare($sql);
        $stmt->execute([$media['title'], $media['description'], $media['file_path'], $media['file_type'], $media['category'], $media['uploaded_by'], $media['file_size']]);
    }
    echo "<div class='alert alert-success'>✅ تم إدراج الوسائط التجريبية</div>";

    echo "<div class='alert alert-info mt-4'>";
    echo "<h4>📊 بيانات تسجيل الدخول التجريبية:</h4>";
    echo "<div class='row'>";
    echo "<div class='col-md-4'>";
    echo "<h6>المدير:</h6>";
    echo "<p><EMAIL><br>admin123</p>";
    echo "</div>";
    echo "<div class='col-md-4'>";
    echo "<h6>ولي أمر:</h6>";
    echo "<p><EMAIL><br>123456</p>";
    echo "</div>";
    echo "<div class='col-md-4'>";
    echo "<h6>أخصائي:</h6>";
    echo "<p><EMAIL><br>123456</p>";
    echo "</div>";
    echo "</div>";
    echo "</div>";

    echo "<div class='text-center mt-4'>";
    echo "<a href='index.php' class='btn btn-primary btn-lg me-3'>الذهاب إلى الصفحة الرئيسية</a>";
    echo "<a href='login.php' class='btn btn-success btn-lg'>تسجيل الدخول</a>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>❌ حدث خطأ أثناء إدراج البيانات</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "</body>";
echo "</html>";
?>

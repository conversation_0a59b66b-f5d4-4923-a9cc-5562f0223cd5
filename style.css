/* 
 * ملف التصميم الأساسي لمنصة نبراس
 * Nibrass Platform Main Stylesheet
 */

/* استيراد الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

/* الإعدادات العامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
    direction: rtl;
    text-align: right;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

/* الألوان الأساسية للمنصة */
:root {
    --primary-color: #4a90e2;
    --secondary-color: #7b68ee;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --nibrass-blue: #2c5aa0;
    --nibrass-green: #4caf50;
    --nibrass-orange: #ff9800;
}

/* الحاوي الرئيسي */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* الشريط العلوي */
.header {
    background: linear-gradient(135deg, var(--nibrass-blue), var(--primary-color));
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 2rem;
    font-weight: 700;
    color: white;
    text-decoration: none;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: var(--nibrass-orange);
}

/* شريط الأخبار */
.news-ticker {
    background-color: var(--nibrass-green);
    color: white;
    padding: 0.5rem 0;
    overflow: hidden;
    white-space: nowrap;
}

.news-ticker-content {
    display: inline-block;
    animation: scroll-right 30s linear infinite;
}

@keyframes scroll-right {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* القسم الرئيسي */
.hero-section {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.9), rgba(44, 90, 160, 0.9)),
                url('images/hero-bg.jpg') center/cover;
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.hero-content h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* الأزرار */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: inherit;
}

.btn-primary {
    background-color: var(--nibrass-orange);
    color: white;
}

.btn-primary:hover {
    background-color: #e68900;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
}

.btn-secondary {
    background-color: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background-color: white;
    color: var(--nibrass-blue);
}

/* البطاقات */
.card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    margin-bottom: 2rem;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.card-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.card h3 {
    color: var(--nibrass-blue);
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

/* الشبكة */
.grid {
    display: grid;
    gap: 2rem;
    margin: 3rem 0;
}

.grid-2 { grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); }
.grid-3 { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }
.grid-4 { grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); }

/* الأقسام */
.section {
    padding: 4rem 0;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    color: var(--nibrass-blue);
    margin-bottom: 3rem;
    font-weight: 700;
}

/* النماذج */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--dark-color);
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    font-family: inherit;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

/* الرسائل */
.alert {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    border: 1px solid transparent;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

/* التذييل */
.footer {
    background-color: var(--dark-color);
    color: white;
    padding: 3rem 0 1rem;
    margin-top: 4rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer h4 {
    color: var(--nibrass-orange);
    margin-bottom: 1rem;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #495057;
    color: #adb5bd;
}

/* تحسينات إضافية للتصميم RTL */
.navbar-toggler {
    border: none;
    padding: 0.25rem 0.5rem;
}

.navbar-toggler:focus {
    box-shadow: none;
}

/* تحسين القوائم المنسدلة */
.dropdown-menu {
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    border: none;
}

.dropdown-item {
    padding: 0.75rem 1rem;
    transition: background-color 0.3s ease;
}

.dropdown-item:hover {
    background-color: var(--light-color);
    color: var(--primary-color);
}

/* تحسين الجداول */
.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.table th {
    background-color: var(--primary-color);
    color: white;
    font-weight: 600;
    border: none;
}

.table td {
    vertical-align: middle;
    border-color: #e9ecef;
}

/* تحسين الشارات */
.badge {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
}

/* تحسين التنبيهات */
.alert {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.alert-dismissible .btn-close {
    padding: 1rem;
}

/* تحسين النماذج */
.form-floating > label {
    right: 0.75rem;
    left: auto;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    transform: scale(.85) translateY(-0.5rem) translateX(0.15rem);
}

/* تحسين الأزرار */
.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

.btn-group .btn:last-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

/* تحسين الصفحات */
.pagination .page-link {
    border-radius: 50%;
    margin: 0 0.25rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 2rem;
    }

    .nav-menu {
        flex-direction: column;
        gap: 1rem;
    }

    .header .container {
        flex-direction: column;
        text-align: center;
    }

    .section-title {
        font-size: 2rem;
    }

    .card {
        margin-bottom: 1rem;
    }

    .grid {
        gap: 1rem;
    }

    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .hero-buttons .btn {
        width: auto;
        margin: 0.25rem;
    }
}

/* أنيميشن التحميل */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* تحسينات إضافية */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mt-3 { margin-top: 1.5rem; }
.p-3 { padding: 1.5rem; }
.d-none { display: none; }
.d-block { display: block; }

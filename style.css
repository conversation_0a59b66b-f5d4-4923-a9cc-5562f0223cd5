/* 
 * ملف التصميم الأساسي لمنصة نبراس
 * Nibrass Platform Main Stylesheet
 */

/* استيراد الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

/* الإعدادات العامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
    direction: rtl;
    text-align: right;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
    padding-top: 80px; /* Space for fixed navbar */
}

/* الألوان الأساسية للمنصة */
:root {
    --primary-color: #4a90e2;
    --secondary-color: #7b68ee;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --nibrass-blue: #2c5aa0;
    --nibrass-green: #4caf50;
    --nibrass-orange: #ff9800;
}

/* الحاوي الرئيسي */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Enhanced Navbar Styles */
.enhanced-navbar {
    background: linear-gradient(135deg, var(--nibrass-blue), var(--primary-color)) !important;
    padding: 1rem 0;
    transition: all 0.3s ease;
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
}

.enhanced-navbar.scrolled {
    padding: 0.5rem 0;
    background: rgba(44, 90, 160, 0.95) !important;
    backdrop-filter: blur(10px);
}

.logo-enhanced {
    display: flex;
    align-items: center;
    font-size: 1.8rem;
    font-weight: 700;
    color: white !important;
    text-decoration: none;
    transition: all 0.3s ease;
}

.logo-enhanced:hover {
    color: var(--nibrass-orange) !important;
    transform: scale(1.05);
}

.logo-icon {
    margin-left: 0.5rem;
    font-size: 2rem;
    animation: pulse 2s infinite;
}

.logo-text {
    background: linear-gradient(45deg, #fff, var(--nibrass-orange));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem !important;
    color: rgba(255,255,255,0.9) !important;
    font-weight: 500;
    border-radius: 25px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.nav-link:hover, .nav-link.active {
    color: white !important;
    background: rgba(255,255,255,0.1);
    transform: translateY(-2px);
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.nav-link:hover::before {
    left: 100%;
}

.nav-icon {
    margin-left: 0.5rem;
    font-size: 1.1rem;
    transition: transform 0.3s ease;
}

.nav-link:hover .nav-icon {
    transform: scale(1.2);
}

.btn-ai-chat {
    background: linear-gradient(45deg, var(--nibrass-green), #4caf50) !important;
    border-radius: 25px !important;
}

.btn-login {
    background: linear-gradient(45deg, var(--nibrass-orange), #ff5722) !important;
    border-radius: 25px !important;
}

.btn-logout {
    background: linear-gradient(45deg, #dc3545, #c82333) !important;
    border-radius: 25px !important;
}

/* Enhanced News Slider */
.news-slider-container {
    background: linear-gradient(135deg, var(--nibrass-green), #4caf50);
    color: white;
    padding: 0.75rem 0;
    position: relative;
    overflow: hidden;
}

.news-slider-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.news-slide {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem;
    position: relative;
    z-index: 1;
}

.news-icon {
    margin-left: 1rem;
    font-size: 1.2rem;
    animation: bounce 2s infinite;
}

.news-text {
    font-weight: 500;
    font-size: 1rem;
}

.carousel-control-prev,
.carousel-control-next {
    width: 40px;
    height: 40px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.7;
    transition: all 0.3s ease;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    opacity: 1;
    background: rgba(255,255,255,0.3);
}

.carousel-control-prev {
    right: 60px;
    left: auto;
}

.carousel-control-next {
    left: 60px;
    right: auto;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-5px); }
    60% { transform: translateY(-3px); }
}

/* Enhanced Hero Section */
.enhanced-hero {
    background: linear-gradient(135deg, var(--nibrass-blue), var(--primary-color));
    color: white;
    padding: 3rem 0 1.5rem;
    min-height: 40vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.enhanced-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent 0%, transparent 70%, var(--light-color) 100%);
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    font-weight: 700;
    line-height: 1.2;
    color: white;
}

.typewriter {
    border-left: 3px solid var(--nibrass-orange);
    padding-right: 10px;
    animation: blink 1s infinite;
    color: white;
}

.hero-description {
    font-size: 1.3rem;
    margin-bottom: 2.5rem;
    line-height: 1.6;
    opacity: 0.95;
    color: white;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: flex-start;
}

.btn-enhanced {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 30px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-enhanced:hover::before {
    left: 100%;
}

.btn-enhanced:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

/* Hero Image Section */
.hero-image {
    position: relative;
    z-index: 2;
}

.hero-image-container {
    position: relative;
    overflow: hidden;
}

.hero-img {
    transition: transform 0.3s ease;
}

.hero-image-container:hover .hero-img {
    transform: scale(1.05);
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.floating-icon {
    position: absolute;
    width: 60px;
    height: 60px;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    animation: float 6s ease-in-out infinite;
}

 .icon-1 { top: 10%; right: 55%; animation-delay: 0s; }
.icon-2 { top: 60%; right: 65%; animation-delay: 1.5s; }
.icon-3 { bottom: 20%; right: 60%; animation-delay: 3s; }
.icon-4 { top: 30%; right: 70%; animation-delay: 4.5s; } 

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes blink {
    0%, 50% { border-color: var(--nibrass-orange); }
    51%, 100% { border-color: transparent; }
}

/* الأزرار */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: inherit;
}

.btn-primary {
    background-color: var(--nibrass-orange);
    color: white;
}

.btn-primary:hover {
    background-color: #e68900;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
}

.btn-secondary {
    background-color: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background-color: white;
    color: var(--nibrass-blue);
}

/* Enhanced Cards */
.card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.4s ease;
    margin-bottom: 2rem;
    border: 1px solid rgba(74, 144, 226, 0.1);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--nibrass-green), var(--nibrass-orange));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.card:hover::before {
    transform: scaleX(1);
}

.card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.card-icon {
    font-size: 3.5rem;
    background: linear-gradient(45deg, var(--primary-color), var(--nibrass-green));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1.5rem;
    transition: transform 0.3s ease;
}

.card:hover .card-icon {
    transform: scale(1.1) rotate(5deg);
}

.card h3 {
    color: var(--nibrass-blue);
    margin-bottom: 1rem;
    font-size: 1.5rem;
    font-weight: 600;
}

.card p {
    line-height: 1.7;
    color: #666;
}

/* Goals Section Enhanced */
.goals-section {
    position: relative;
}

.goals-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 3rem;
}

.goal-card {
    height: 100%;
    transition: all 0.4s ease;
}

.goal-card .card-body {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 2.5rem 1.5rem;
}

.goal-card .card-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
}

.goal-card h3 {
    font-size: 1.4rem;
    margin-bottom: 1rem;
    color: var(--nibrass-blue);
}

.goal-card p {
    font-size: 1rem;
    line-height: 1.6;
    color: #666;
    margin: 0;
}

/* Space Cards */
.space-card {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border: 2px solid transparent;
    background-clip: padding-box;
    transition: all 0.4s ease;
}

.space-card:hover {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, #fff 0%, #f0f8ff 100%);
}

.space-card .card-body {
    padding: 2.5rem 1.5rem;
}

.space-card .card-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
}

.space-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--nibrass-blue);
}

.space-card p {
    font-size: 1rem;
    line-height: 1.6;
    color: #666;
    margin-bottom: 1.5rem;
}

/* الشبكة */
.grid {
    display: grid;
    gap: 2rem;
    margin: 3rem 0;
}

.grid-2 { grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); }
.grid-3 { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }
.grid-4 { grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); }

/* الأقسام */
.section {
    padding: 4rem 0;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    color: var(--nibrass-blue);
    margin-bottom: 3rem;
    font-weight: 700;
}

/* النماذج */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--dark-color);
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    font-family: inherit;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

/* الرسائل */
.alert {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    border: 1px solid transparent;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

/* Enhanced Footer */
.footer {
    background: linear-gradient(135deg, var(--dark-color), #2c3e50);
    color: #ffffff;
    padding: 4rem 0 1rem;
    margin-top: 4rem;
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--nibrass-green), var(--nibrass-orange));
}

.footer::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><defs><pattern id="footerPattern" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100%" height="100%" fill="url(%23footerPattern)"/></svg>');
    opacity: 0.3;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 3rem;
    margin-bottom: 3rem;
    position: relative;
    z-index: 1;
}

.footer-section h4 {
    color: var(--nibrass-orange);
    margin-bottom: 1.5rem;
    font-weight: 600;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.footer-section p {
    color: #e9ecef;
    line-height: 1.7;
    margin-bottom: 1rem;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.75rem;
}

.footer-links a {
    color: #e9ecef;
    text-decoration: none;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0;
}

.footer-links a:hover {
    color: var(--nibrass-orange);
    transform: translateX(-5px);
}

.footer-links a i {
    width: 16px;
    text-align: center;
}

.contact-info p {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    color: #e9ecef;
}

.contact-info i {
    width: 16px;
    text-align: center;
    color: var(--nibrass-orange);
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.social-link {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.social-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    transform: scale(0);
    transition: transform 0.3s ease;
}

.social-link:hover::before {
    transform: scale(1);
}

.social-link:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.facebook { background: #3b5998; }
.facebook::before { background: #2d4373; }

.twitter { background: #1da1f2; }
.twitter::before { background: #0d8bd9; }

.instagram { background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888); }
.instagram::before { background: linear-gradient(45deg, #d6831f, #c9562c, #b91d39, #a91e52, #9a1674); }

.youtube { background: #ff0000; }
.youtube::before { background: #cc0000; }

.footer-stats {
    display: flex;
    gap: 1.5rem;
    margin-top: 1rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #e9ecef;
    font-size: 0.9rem;
}

.stat-item i {
    color: var(--nibrass-orange);
}

.footer-bottom {
    padding-top: 2rem;
    border-top: 1px solid #495057;
    position: relative;
    z-index: 1;
}

.footer-bottom p {
    color: #e9ecef;
    margin: 0;
    font-size: 0.95rem;
}

.footer-bottom .text-danger {
    color: #ff6b6b !important;
    animation: heartbeat 1.5s ease-in-out infinite;
}

@keyframes heartbeat {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* تحسينات إضافية للتصميم RTL */
.navbar-toggler {
    border: none;
    padding: 0.25rem 0.5rem;
}

.navbar-toggler:focus {
    box-shadow: none;
}

/* تحسين القوائم المنسدلة */
.dropdown-menu {
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    border: none;
}

.dropdown-item {
    padding: 0.75rem 1rem;
    transition: background-color 0.3s ease;
}

.dropdown-item:hover {
    background-color: var(--light-color);
    color: var(--primary-color);
}

/* تحسين الجداول */
.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.table th {
    background-color: var(--primary-color);
    color: white;
    font-weight: 600;
    border: none;
}

.table td {
    vertical-align: middle;
    border-color: #e9ecef;
}

/* تحسين الشارات */
.badge {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
}

/* تحسين التنبيهات */
.alert {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.alert-dismissible .btn-close {
    padding: 1rem;
}

/* تحسين النماذج */
.form-floating > label {
    right: 0.75rem;
    left: auto;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    transform: scale(.85) translateY(-0.5rem) translateX(0.15rem);
}

/* تحسين الأزرار */
.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

.btn-group .btn:last-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

/* تحسين الصفحات */
.pagination .page-link {
    border-radius: 50%;
    margin: 0 0.25rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Scroll Animations */
.scroll-animate {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.scroll-animate.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.fade-in-up {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 1s ease forwards;
    animation-delay: 0.5s;
}

.fade-in-right {
    opacity: 0;
    transform: translateX(30px);
    animation: fadeInRight 1s ease forwards;
    animation-delay: 0.8s;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
    .goals-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .enhanced-hero {
        padding: 2rem 0 1rem;
        min-height: 35vh;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-description {
        font-size: 1.1rem;
    }

    .hero-buttons {
        justify-content: center;
        flex-direction: column;
        align-items: center;
    }

    .btn-enhanced {
        width: 100%;
        max-width: 300px;
        margin-bottom: 0.5rem;
    }

    .goals-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .goal-card .card-body {
        padding: 2rem 1rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .card {
        margin-bottom: 1rem;
    }

    .news-slider-container {
        padding: 0.5rem 0;
    }

    .news-text {
        font-size: 0.9rem;
    }

    .carousel-control-prev,
    .carousel-control-next {
        width: 35px;
        height: 35px;
    }

    .carousel-control-prev {
        right: 45px;
    }

    .carousel-control-next {
        left: 45px;
    }

    .floating-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .enhanced-navbar {
        padding: 0.5rem 0;
    }

    .logo-enhanced {
        font-size: 1.5rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .goals-grid {
        gap: 0.75rem;
    }

    .goal-card .card-body {
        padding: 1.5rem 0.75rem;
    }

    .goal-card .card-icon {
        font-size: 3rem;
    }

    .goal-card h3 {
        font-size: 1.2rem;
    }

    .goal-card p {
        font-size: 0.9rem;
    }

    .section-title {
        font-size: 1.75rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .footer-stats {
        justify-content: center;
    }

    .social-links {
        justify-content: center;
    }

    .footer-bottom .row {
        text-align: center;
    }

    .footer-bottom .col-md-6 {
        margin-bottom: 0.5rem;
    }
}

/* أنيميشن التحميل */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* تحسينات إضافية */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mt-3 { margin-top: 1.5rem; }
.p-3 { padding: 1.5rem; }
.d-none { display: none; }
.d-block { display: block; }

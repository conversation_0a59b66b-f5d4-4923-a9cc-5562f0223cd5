/**
 * ملف JavaScript الرئيسي لمنصة نبراس
 * Main JavaScript file for Nibrass Platform
 */

// تهيئة المتغيرات العامة
const NibrassApp = {
    init: function() {
        this.initSmoothScroll();
        this.initScrollAnimations();
        this.initNavbar();
        this.initTypewriter();
        this.initFloatingIcons();
        this.initCardHovers();
        this.initNewsCarousel();
    },

    // تمرير سلس للروابط
    initSmoothScroll: function() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    },

    // تحريك العناصر عند التمرير
    initScrollAnimations: function() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const delay = entry.target.dataset.delay || 0;
                    setTimeout(() => {
                        entry.target.classList.add('animate-in');
                    }, delay);
                }
            });
        }, observerOptions);

        // مراقبة جميع العناصر المحركة
        document.querySelectorAll('.scroll-animate').forEach(el => {
            observer.observe(el);
        });
    },

    // تأثيرات شريط التنقل
    initNavbar: function() {
        window.addEventListener('scroll', () => {
            const navbar = document.querySelector('.enhanced-navbar');
            if (navbar) {
                if (window.scrollY > 50) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }
            }
        });
    },

    // تأثير الكتابة للعنوان الرئيسي
    initTypewriter: function() {
        const typeWriter = (element, text, speed = 100) => {
            let i = 0;
            element.innerHTML = '';
            
            function type() {
                if (i < text.length) {
                    element.innerHTML += text.charAt(i);
                    i++;
                    setTimeout(type, speed);
                }
            }
            type();
        };

        // تشغيل تأثير الكتابة عند تحميل الصفحة
        window.addEventListener('load', () => {
            const heroTitle = document.querySelector('.typewriter');
            if (heroTitle) {
                const text = heroTitle.textContent;
                typeWriter(heroTitle, text, 80);
            }
        });
    },

    // تحريك الأيقونات العائمة
    initFloatingIcons: function() {
        const icons = document.querySelectorAll('.floating-icon');
        icons.forEach((icon, index) => {
            icon.style.animationDelay = `${index * 0.5}s`;
        });
    },

    // تأثيرات البطاقات عند التمرير
    initCardHovers: function() {
        document.querySelectorAll('.goal-card, .space-card, .creative-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    },

    // إعدادات شريط الأخبار
    initNewsCarousel: function() {
        const newsCarousel = document.getElementById('newsCarousel');
        if (newsCarousel) {
            // إيقاف التشغيل التلقائي عند التمرير
            newsCarousel.addEventListener('mouseenter', () => {
                const carousel = bootstrap.Carousel.getInstance(newsCarousel);
                if (carousel) carousel.pause();
            });
            
            // استئناف التشغيل التلقائي عند مغادرة المؤشر
            newsCarousel.addEventListener('mouseleave', () => {
                const carousel = bootstrap.Carousel.getInstance(newsCarousel);
                if (carousel) carousel.cycle();
            });
        }
    }
};

// دوال مساعدة عامة
const NibrassHelpers = {
    // عرض رسالة تنبيه
    showAlert: function(message, type = 'info', duration = 5000) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 300px;';
        
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        // إزالة التنبيه تلقائياً
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, duration);
    },

    // تحميل محتوى بـ AJAX
    loadContent: function(url, targetElement, callback) {
        fetch(url)
            .then(response => response.text())
            .then(data => {
                if (targetElement) {
                    targetElement.innerHTML = data;
                }
                if (callback) callback(data);
            })
            .catch(error => {
                console.error('Error loading content:', error);
                this.showAlert('حدث خطأ أثناء تحميل المحتوى', 'danger');
            });
    },

    // تنسيق التاريخ بالعربية
    formatArabicDate: function(date) {
        const options = { 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric',
            weekday: 'long'
        };
        return new Intl.DateTimeFormat('ar-TN', options).format(new Date(date));
    },

    // التحقق من صحة البريد الإلكتروني
    validateEmail: function(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    },

    // التحقق من صحة رقم الهاتف التونسي
    validateTunisianPhone: function(phone) {
        const re = /^(\+216|216)?[2-9]\d{7}$/;
        return re.test(phone.replace(/\s/g, ''));
    }
};

// دوال خاصة بالصفحات
const PageSpecific = {
    // دوال خاصة بصفحة المكتبة
    library: {
        initSearch: function() {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    const cards = document.querySelectorAll('.card');
                    
                    cards.forEach(card => {
                        const title = card.querySelector('.card-title');
                        const text = card.querySelector('.card-text');
                        
                        if (title && text) {
                            const titleText = title.textContent.toLowerCase();
                            const bodyText = text.textContent.toLowerCase();
                            
                            if (titleText.includes(searchTerm) || bodyText.includes(searchTerm)) {
                                card.closest('.col-lg-4, .col-lg-3, .col-lg-6, .col-md-6, .col-md-4, .col-sm-6').style.display = 'block';
                            } else {
                                card.closest('.col-lg-4, .col-lg-3, .col-lg-6, .col-md-6, .col-md-4, .col-sm-6').style.display = 'none';
                            }
                        }
                    });
                });
            }
        }
    },

    // دوال خاصة بصفحة الورشات
    workshops: {
        registerWorkshop: function(workshopId) {
            if (confirm('هل تريد التسجيل في هذه الورشة؟')) {
                // يمكن إضافة كود التسجيل هنا
                NibrassHelpers.showAlert('تم التسجيل بنجاح! سيتم التواصل معك قريباً.', 'success');
            }
        }
    },

    // دوال خاصة بصفحة الإبداع
    creativity: {
        startActivity: function(activityType) {
            const activities = {
                'drawing': '🎨 مرحباً بك في عالم الرسم! سنفتح أدوات الرسم قريباً...',
                'music': '🎵 استعد للاستماع إلى أجمل الأناشيد التعليمية!',
                'videos': '🎬 فيديوهات ممتعة ومفيدة في انتظارك!',
                'photography': '📸 شارك أجمل لحظاتك معنا!'
            };
            
            const message = activities[activityType] || 'نشاط ممتع ينتظرك!';
            NibrassHelpers.showAlert(message, 'info');
        }
    }
};

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    NibrassApp.init();
    
    // تهيئة الدوال الخاصة بالصفحة الحالية
    const currentPage = document.body.dataset.page;
    if (currentPage && PageSpecific[currentPage]) {
        Object.keys(PageSpecific[currentPage]).forEach(method => {
            if (typeof PageSpecific[currentPage][method] === 'function') {
                PageSpecific[currentPage][method]();
            }
        });
    }
});

// تصدير الدوال للاستخدام العام
window.NibrassApp = NibrassApp;
window.NibrassHelpers = NibrassHelpers;
window.PageSpecific = PageSpecific;

/* 
 * ملف CSS خاص بصفحة الورشات
 * Workshops Page Specific Styles
 */

.workshop-card {
    border: none;
    border-radius: 20px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
}

.workshop-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.workshop-header {
    background: linear-gradient(135deg, #4caf50, #8bc34a);
    color: white;
    padding: 1.5rem;
    position: relative;
}

.workshop-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 20px;
    background: white;
    border-radius: 20px 20px 0 0;
}

.workshop-date {
    background: rgba(255,255,255,0.2);
    border-radius: 15px;
    padding: 0.5rem 1rem;
    display: inline-block;
    margin-bottom: 1rem;
}

.instructor-badge {
    background: var(--nibrass-orange);
    color: white;
    padding: 0.3rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.participants-info {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
    margin: 1rem 0;
}

.register-btn {
    background: linear-gradient(135deg, #ff9800, #ff5722);
    border: none;
    border-radius: 25px;
    padding: 0.75rem 2rem;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
}

.register-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 152, 0, 0.3);
    color: white;
}

.past-workshop {
    opacity: 0.8;
    border: 2px solid #e9ecef;
}

.workshop-category {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: rgba(255,255,255,0.9);
    color: var(--nibrass-blue);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* تحسينات إضافية للورشات */
.workshop-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

.workshop-stat {
    text-align: center;
    flex: 1;
}

.workshop-stat i {
    color: var(--primary-color);
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

.workshop-stat span {
    display: block;
    font-size: 0.9rem;
    color: #666;
}

.workshop-level {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.level-beginner {
    background: #d4edda;
    color: #155724;
}

.level-intermediate {
    background: #fff3cd;
    color: #856404;
}

.level-advanced {
    background: #f8d7da;
    color: #721c24;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .workshop-card {
        margin-bottom: 1.5rem;
    }
    
    .workshop-header {
        padding: 1rem;
    }
    
    .workshop-stats {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .workshop-stat {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        text-align: right;
    }
    
    .register-btn {
        width: 100%;
        margin-top: 1rem;
    }
}

-- جداول نظام الاستشارات المحسن لمنصة نبراس

-- جدول الاستشارات الرئيسي
CREATE TABLE IF NOT EXISTS consultations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    parent_id INT NOT NULL,
    student_id INT NULL,
    specialist_id INT NULL,
    consultation_type ENUM('behavioral', 'educational', 'medical', 'developmental') NOT NULL,
    urgency_level ENUM('urgent', 'normal', 'routine') NOT NULL DEFAULT 'normal',
    subject VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    preferred_date DATE NULL,
    preferred_time ENUM('morning', 'afternoon', 'evening') NULL,
    status ENUM('pending', 'assigned', 'scheduled', 'in_progress', 'completed', 'cancelled') NOT NULL DEFAULT 'pending',
    assigned_at TIMESTAMP NULL,
    scheduled_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (parent_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE SET NULL,
    FOREIGN KEY (specialist_id) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_parent_id (parent_id),
    INDEX idx_specialist_id (specialist_id),
    INDEX idx_status (status),
    INDEX idx_urgency (urgency_level),
    INDEX idx_created_at (created_at)
);

-- جدول مرفقات الاستشارات
CREATE TABLE IF NOT EXISTS consultation_attachments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    consultation_id INT NOT NULL,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL DEFAULT 0,
    mime_type VARCHAR(100) NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (consultation_id) REFERENCES consultations(id) ON DELETE CASCADE,
    INDEX idx_consultation_id (consultation_id)
);

-- جدول ردود الاستشارات
CREATE TABLE IF NOT EXISTS consultation_responses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    consultation_id INT NOT NULL,
    specialist_id INT NOT NULL,
    response_text TEXT NOT NULL,
    is_private BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (consultation_id) REFERENCES consultations(id) ON DELETE CASCADE,
    FOREIGN KEY (specialist_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_consultation_id (consultation_id),
    INDEX idx_specialist_id (specialist_id)
);

-- جدول المواعيد
CREATE TABLE IF NOT EXISTS appointments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    consultation_id INT NULL,
    parent_id INT NOT NULL,
    specialist_id INT NOT NULL,
    student_id INT NULL,
    appointment_type ENUM('consultation', 'follow_up', 'assessment', 'therapy') NOT NULL DEFAULT 'consultation',
    appointment_date DATETIME NOT NULL,
    duration_minutes INT NOT NULL DEFAULT 60,
    location VARCHAR(255) NULL,
    meeting_link VARCHAR(500) NULL,
    notes TEXT NULL,
    status ENUM('scheduled', 'confirmed', 'completed', 'cancelled', 'no_show') NOT NULL DEFAULT 'scheduled',
    reminder_sent BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (consultation_id) REFERENCES consultations(id) ON DELETE SET NULL,
    FOREIGN KEY (parent_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (specialist_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE SET NULL,
    
    INDEX idx_parent_id (parent_id),
    INDEX idx_specialist_id (specialist_id),
    INDEX idx_appointment_date (appointment_date),
    INDEX idx_status (status)
);

-- جدول تقييمات الاستشارات
CREATE TABLE IF NOT EXISTS consultation_ratings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    consultation_id INT NOT NULL,
    parent_id INT NOT NULL,
    specialist_id INT NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    feedback TEXT NULL,
    would_recommend BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (consultation_id) REFERENCES consultations(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (specialist_id) REFERENCES users(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_consultation_rating (consultation_id, parent_id),
    INDEX idx_specialist_id (specialist_id),
    INDEX idx_rating (rating)
);

-- جدول إشعارات الاستشارات
CREATE TABLE IF NOT EXISTS consultation_notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    consultation_id INT NOT NULL,
    user_id INT NOT NULL,
    notification_type ENUM('new_consultation', 'response_received', 'appointment_scheduled', 'appointment_reminder', 'status_update') NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (consultation_id) REFERENCES consultations(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_user_id (user_id),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at)
);

-- جدول أنشطة الطلاب (للتتبع والتقارير)
CREATE TABLE IF NOT EXISTS student_activities (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    activity_id INT NULL,
    activity_type ENUM('game', 'exercise', 'assessment', 'therapy_session') NOT NULL,
    activity_title VARCHAR(255) NOT NULL,
    description TEXT NULL,
    score INT NULL,
    max_score INT NULL,
    duration_minutes INT NULL,
    completed_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    INDEX idx_student_id (student_id),
    INDEX idx_activity_type (activity_type),
    INDEX idx_completed_at (completed_at)
);

-- جدول تقارير التقدم
CREATE TABLE IF NOT EXISTS progress_reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    specialist_id INT NOT NULL,
    report_period_start DATE NOT NULL,
    report_period_end DATE NOT NULL,
    overall_progress ENUM('excellent', 'good', 'satisfactory', 'needs_improvement') NOT NULL,
    behavioral_notes TEXT NULL,
    educational_notes TEXT NULL,
    social_notes TEXT NULL,
    recommendations TEXT NULL,
    goals_for_next_period TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (specialist_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_student_id (student_id),
    INDEX idx_specialist_id (specialist_id),
    INDEX idx_report_period (report_period_start, report_period_end)
);

-- إدراج بيانات تجريبية للأخصائيين
INSERT IGNORE INTO users (name, email, password, role, specialization, is_active, created_at) VALUES
('د. أحمد محمد', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'specialist', 'اضطراب طيف التوحد', 1, NOW()),
('د. فاطمة علي', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'specialist', 'علاج النطق واللغة', 1, NOW()),
('د. محمد حسن', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'specialist', 'العلاج السلوكي', 1, NOW()),
('د. سارة أحمد', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'specialist', 'التربية الخاصة', 1, NOW());

-- إدراج بيانات تجريبية للأولياء
INSERT IGNORE INTO users (name, email, password, role, is_active, created_at) VALUES
('أحمد الأب', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'parent', 1, NOW());

-- إدراج بيانات تجريبية للطلاب
INSERT IGNORE INTO students (name, age, parent_id, diagnosis_level, progress_level, weekly_stars, total_stars, created_at) VALUES
('محمد أحمد', 8, (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1), 'متوسط', 75, 12, 156, NOW()),
('سارة أحمد', 6, (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1), 'خفيف', 85, 15, 203, NOW());

-- إدراج بيانات تجريبية للاستشارات
INSERT IGNORE INTO consultations (parent_id, student_id, specialist_id, consultation_type, urgency_level, subject, description, status, created_at) VALUES
((SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1), 
 (SELECT id FROM students WHERE name = 'محمد أحمد' LIMIT 1),
 (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
 'behavioral', 'normal', 'صعوبة في التواصل الاجتماعي', 
 'طفلي يواجه صعوبة في التفاعل مع الأطفال الآخرين في المدرسة ولا يشارك في الأنشطة الجماعية', 
 'pending', NOW()),
((SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1), 
 (SELECT id FROM students WHERE name = 'سارة أحمد' LIMIT 1),
 (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
 'educational', 'routine', 'تطوير مهارات النطق', 
 'أريد تحسين مهارات النطق لدى ابنتي وتطوير قدرتها على التعبير عن احتياجاتها', 
 'completed', DATE_SUB(NOW(), INTERVAL 7 DAY));

-- إدراج بيانات تجريبية للأنشطة
INSERT IGNORE INTO student_activities (student_id, activity_type, activity_title, description, score, max_score, duration_minutes, completed_at) VALUES
((SELECT id FROM students WHERE name = 'محمد أحمد' LIMIT 1), 'game', 'لعبة الألوان', 'تعلم الألوان الأساسية', 85, 100, 15, DATE_SUB(NOW(), INTERVAL 2 DAY)),
((SELECT id FROM students WHERE name = 'محمد أحمد' LIMIT 1), 'exercise', 'تمرين التركيز', 'تمارين لتحسين التركيز والانتباه', 78, 100, 20, DATE_SUB(NOW(), INTERVAL 1 DAY)),
((SELECT id FROM students WHERE name = 'سارة أحمد' LIMIT 1), 'game', 'لعبة الحروف', 'تعلم الحروف الأبجدية', 92, 100, 18, DATE_SUB(NOW(), INTERVAL 3 DAY)),
((SELECT id FROM students WHERE name = 'سارة أحمد' LIMIT 1), 'assessment', 'تقييم المهارات الاجتماعية', 'تقييم شامل للمهارات الاجتماعية', 88, 100, 30, DATE_SUB(NOW(), INTERVAL 5 DAY));

-- إدراج بيانات تجريبية للمواعيد
INSERT IGNORE INTO appointments (parent_id, specialist_id, student_id, appointment_type, appointment_date, duration_minutes, status, created_at) VALUES
((SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
 (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
 (SELECT id FROM students WHERE name = 'محمد أحمد' LIMIT 1),
 'consultation', DATE_ADD(NOW(), INTERVAL 3 DAY), 60, 'scheduled', NOW()),
((SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
 (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
 (SELECT id FROM students WHERE name = 'سارة أحمد' LIMIT 1),
 'follow_up', DATE_ADD(NOW(), INTERVAL 7 DAY), 45, 'scheduled', NOW());

-- جدول رسائل الذكاء الاصطناعي
CREATE TABLE IF NOT EXISTS ai_chat_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    message TEXT NOT NULL,
    sender ENUM('user', 'ai') NOT NULL,
    session_id VARCHAR(100) NULL,
    tokens_used INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_session_id (session_id),
    INDEX idx_created_at (created_at)
);

-- جدول إحصائيات استخدام الذكاء الاصطناعي
CREATE TABLE IF NOT EXISTS ai_usage_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    date DATE NOT NULL,
    messages_sent INT DEFAULT 0,
    tokens_used INT DEFAULT 0,
    session_duration_minutes INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_date (user_id, date),
    INDEX idx_date (date)
);

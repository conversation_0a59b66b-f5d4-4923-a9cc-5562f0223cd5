<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كولي أمر
require_parent();

$user = current_user();

// معالجة طلبات AJAX للدردشة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    if ($_POST['action'] == 'send_message') {
        $message = sanitize_input($_POST['message']);
        
        if (empty($message)) {
            echo json_encode(['success' => false, 'error' => 'الرسالة فارغة']);
            exit();
        }
        
        // حفظ رسالة المستخدم
        $save_query = "INSERT INTO ai_chat_messages (user_id, message, sender, created_at) VALUES (:user_id, :message, 'user', NOW())";
        $save_stmt = $db->prepare($save_query);
        $save_stmt->bindParam(':user_id', $user['id']);
        $save_stmt->bindParam(':message', $message);
        $save_stmt->execute();
        
        // إرسال الرسالة لـ OpenAI
        $ai_response = sendToOpenAI($message, $user);
        
        if ($ai_response) {
            // حفظ رد الذكاء الاصطناعي
            $save_ai_query = "INSERT INTO ai_chat_messages (user_id, message, sender, created_at) VALUES (:user_id, :message, 'ai', NOW())";
            $save_ai_stmt = $db->prepare($save_ai_query);
            $save_ai_stmt->bindParam(':user_id', $user['id']);
            $save_ai_stmt->bindParam(':message', $ai_response);
            $save_ai_stmt->execute();
            
            echo json_encode(['success' => true, 'response' => $ai_response]);
        } else {
            echo json_encode(['success' => false, 'error' => 'عذراً، حدث خطأ في الاتصال بالمساعد الذكي']);
        }
        exit();
    }
    
    if ($_POST['action'] == 'get_history') {
        $history_query = "SELECT message, sender, created_at FROM ai_chat_messages 
                         WHERE user_id = :user_id 
                         ORDER BY created_at ASC 
                         LIMIT 50";
        $history_stmt = $db->prepare($history_query);
        $history_stmt->bindParam(':user_id', $user['id']);
        $history_stmt->execute();
        $messages = $history_stmt->fetchAll();
        
        echo json_encode(['success' => true, 'messages' => $messages]);
        exit();
    }
}

// دالة إرسال الرسالة لـ OpenAI
function sendToOpenAI($message, $user) {
    $api_key = '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';
    
    // إعداد السياق للمساعد الذكي
    $system_prompt = "أنت مساعد ذكي متخصص في دعم أولياء أمور الأطفال ذوي اضطراب طيف التوحد. 
    اسمك هو 'نبراس' وأنت جزء من منصة نبراس التعليمية.
    
    مهامك:
    - تقديم المشورة والدعم لأولياء الأمور
    - الإجابة على الأسئلة حول اضطراب طيف التوحد
    - تقديم نصائح تربوية وتعليمية
    - توجيه الأولياء للموارد المناسبة
    - تقديم الدعم العاطفي والمعنوي
    
    قواعد مهمة:
    - أجب باللغة العربية دائماً
    - كن متفهماً ومتعاطفاً
    - قدم معلومات دقيقة ومفيدة
    - إذا لم تكن متأكداً من شيء، انصح بالتواصل مع أخصائي
    - لا تقدم تشخيصات طبية
    - ركز على الجوانب الإيجابية والحلول العملية
    
    المستخدم الحالي: " . $user['name'];
    
    $data = [
        'model' => 'gpt-3.5-turbo',
        'messages' => [
            [
                'role' => 'system',
                'content' => $system_prompt
            ],
            [
                'role' => 'user',
                'content' => $message
            ]
        ],
        'max_tokens' => 500,
        'temperature' => 0.7
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://api.openai.com/v1/chat/completions');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $api_key
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code == 200) {
        $result = json_decode($response, true);
        if (isset($result['choices'][0]['message']['content'])) {
            return trim($result['choices'][0]['message']['content']);
        }
    }
    
    return false;
}

// إعداد متغيرات الصفحة
$page_title = 'المساعد الذكي - نبراس';
$page_description = 'تحدث مع المساعد الذكي للحصول على الدعم والمشورة';

// تضمين الهيدر
include '../includes/header.php';
?>

<style>
.ai-chat-header {
    background: linear-gradient(135deg, #4a90e2, #2c5aa0, #1e3a8a, #3b82f6);
    background-size: 400% 400%;
    animation: gradientShift 8s ease infinite;
    color: white !important;
    padding: 3rem 0;
    border-radius: 0 0 30px 30px;
}

.ai-chat-header h1,
.ai-chat-header p {
    color: white !important;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.chat-container {
    background: white;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    overflow: hidden;
    height: 600px;
    display: flex;
    flex-direction: column;
}

.chat-header {
    background: linear-gradient(135deg, var(--primary-color), #2c5aa0);
    color: white;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.chat-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255,255,255,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.chat-messages {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    background: #f8f9fa;
}

.message {
    margin-bottom: 1rem;
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.message.user {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
    flex-shrink: 0;
}

.message.user .message-avatar {
    background: var(--primary-color);
}

.message.ai .message-avatar {
    background: var(--nibrass-green);
}

.message-content {
    max-width: 70%;
    padding: 1rem;
    border-radius: 15px;
    position: relative;
}

.message.user .message-content {
    background: var(--primary-color);
    color: white;
    border-bottom-left-radius: 5px;
}

.message.ai .message-content {
    background: white;
    color: #333;
    border: 1px solid #e9ecef;
    border-bottom-right-radius: 5px;
}

.message-time {
    font-size: 0.75rem;
    opacity: 0.7;
    margin-top: 0.5rem;
}

.chat-input {
    padding: 1rem;
    background: white;
    border-top: 1px solid #e9ecef;
}

.input-group {
    position: relative;
}

.chat-input-field {
    border-radius: 25px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    padding-left: 3rem;
}

.chat-input-field:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
}

.send-button {
    border-radius: 50%;
    width: 45px;
    height: 45px;
    border: none;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.send-button:hover {
    background: #2c5aa0;
    transform: scale(1.05);
}

.send-button:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
}

.typing-indicator {
    display: none;
    padding: 1rem;
    color: #666;
    font-style: italic;
}

.typing-dots {
    display: inline-block;
    animation: typing 1.5s infinite;
}

@keyframes typing {
    0%, 60%, 100% { opacity: 0; }
    30% { opacity: 1; }
}

.quick-suggestions {
    padding: 1rem;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.suggestion-chip {
    display: inline-block;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 20px;
    padding: 0.5rem 1rem;
    margin: 0.25rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.suggestion-chip:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.welcome-message {
    text-align: center;
    padding: 2rem;
    color: #666;
}

.welcome-message i {
    font-size: 4rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}
</style>

<!-- قسم العنوان -->
<section class="ai-chat-header">
    <div class="container">
        <div class="text-center">
            <h1><i class="fas fa-robot"></i> المساعد الذكي - نبراس</h1>
            <p class="lead">احصل على الدعم والمشورة من المساعد الذكي المتخصص في اضطراب طيف التوحد</p>
        </div>
    </div>
</section>

<!-- واجهة الدردشة -->
<section class="section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="chat-container">
                    <!-- رأس الدردشة -->
                    <div class="chat-header">
                        <div class="chat-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div>
                            <h5 class="mb-0">نبراس - المساعد الذكي</h5>
                            <small>متصل الآن</small>
                        </div>
                        <div class="ms-auto">
                            <button class="btn btn-sm btn-outline-light" onclick="clearChat()">
                                <i class="fas fa-trash"></i> مسح المحادثة
                            </button>
                        </div>
                    </div>
                    
                    <!-- منطقة الرسائل -->
                    <div class="chat-messages" id="chatMessages">
                        <div class="welcome-message">
                            <i class="fas fa-robot"></i>
                            <h4>مرحباً <?php echo htmlspecialchars($user['name']); ?>!</h4>
                            <p>أنا نبراس، المساعد الذكي لمنصة نبراس. أنا هنا لمساعدتك في كل ما يتعلق بدعم طفلك ذي اضطراب طيف التوحد.</p>
                            <p class="small text-muted">يمكنك سؤالي عن أي شيء أو اختيار أحد الاقتراحات أدناه</p>
                        </div>
                    </div>
                    
                    <!-- مؤشر الكتابة -->
                    <div class="typing-indicator" id="typingIndicator">
                        <div class="message ai">
                            <div class="message-avatar">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="message-content">
                                نبراس يكتب<span class="typing-dots">...</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- الاقتراحات السريعة -->
                    <div class="quick-suggestions">
                        <div class="suggestion-chip" onclick="sendSuggestion('كيف يمكنني تحسين التواصل مع طفلي؟')">
                            كيف يمكنني تحسين التواصل مع طفلي؟
                        </div>
                        <div class="suggestion-chip" onclick="sendSuggestion('ما هي أفضل الأنشطة للأطفال ذوي التوحد؟')">
                            ما هي أفضل الأنشطة للأطفال ذوي التوحد؟
                        </div>
                        <div class="suggestion-chip" onclick="sendSuggestion('كيف أتعامل مع نوبات الغضب؟')">
                            كيف أتعامل مع نوبات الغضب؟
                        </div>
                        <div class="suggestion-chip" onclick="sendSuggestion('نصائح لتطوير المهارات الاجتماعية')">
                            نصائح لتطوير المهارات الاجتماعية
                        </div>
                    </div>
                    
                    <!-- منطقة الإدخال -->
                    <div class="chat-input">
                        <div class="input-group">
                            <input type="text" class="form-control chat-input-field" 
                                   id="messageInput" placeholder="اكتب رسالتك هنا..." 
                                   maxlength="500">
                            <button class="btn send-button" id="sendButton" onclick="sendMessage()">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                        <div class="text-center mt-2">
                            <small class="text-muted">
                                <i class="fas fa-shield-alt"></i>
                                محادثاتك محمية ومشفرة
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-4">
            <a href="dashboard.php" class="btn btn-outline-primary">
                <i class="fas fa-arrow-right"></i> العودة لوحة التحكم
            </a>
        </div>
    </div>
</section>

<?php
// إضافة JavaScript خاص بالصفحة
$page_js = "
    let isTyping = false;
    
    // تحميل تاريخ المحادثة عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        loadChatHistory();
        
        // إرسال الرسالة عند الضغط على Enter
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
    });
    
    function loadChatHistory() {
        fetch('ai-chat.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=get_history'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.messages.length > 0) {
                const welcomeMessage = document.querySelector('.welcome-message');
                if (welcomeMessage) {
                    welcomeMessage.style.display = 'none';
                }
                
                data.messages.forEach(message => {
                    addMessageToChat(message.sender, message.message, message.created_at);
                });
            }
        })
        .catch(error => {
            console.error('Error loading chat history:', error);
        });
    }
    
    function sendMessage() {
        const input = document.getElementById('messageInput');
        const message = input.value.trim();
        
        if (message && !isTyping) {
            // إخفاء رسالة الترحيب
            const welcomeMessage = document.querySelector('.welcome-message');
            if (welcomeMessage) {
                welcomeMessage.style.display = 'none';
            }
            
            // إضافة رسالة المستخدم
            addMessageToChat('user', message);
            input.value = '';
            
            // إظهار مؤشر الكتابة
            showTypingIndicator();
            
            // إرسال الرسالة للخادم
            fetch('ai-chat.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=send_message&message=' + encodeURIComponent(message)
            })
            .then(response => response.json())
            .then(data => {
                hideTypingIndicator();
                
                if (data.success) {
                    addMessageToChat('ai', data.response);
                } else {
                    addMessageToChat('ai', 'عذراً، حدث خطأ. يرجى المحاولة مرة أخرى.');
                }
            })
            .catch(error => {
                hideTypingIndicator();
                addMessageToChat('ai', 'عذراً، حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.');
                console.error('Error:', error);
            });
        }
    }
    
    function sendSuggestion(suggestion) {
        document.getElementById('messageInput').value = suggestion;
        sendMessage();
    }
    
    function addMessageToChat(sender, message, timestamp = null) {
        const chatMessages = document.getElementById('chatMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message ' + sender;
        
        const time = timestamp ? new Date(timestamp).toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'}) : new Date().toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'});
        
        messageDiv.innerHTML = `
            <div class='message-avatar'>
                <i class='fas fa-${sender === 'user' ? 'user' : 'robot'}'></i>
            </div>
            <div class='message-content'>
                ${message}
                <div class='message-time'>${time}</div>
            </div>
        `;
        
        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
    
    function showTypingIndicator() {
        isTyping = true;
        document.getElementById('typingIndicator').style.display = 'block';
        document.getElementById('sendButton').disabled = true;
        
        const chatMessages = document.getElementById('chatMessages');
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
    
    function hideTypingIndicator() {
        isTyping = false;
        document.getElementById('typingIndicator').style.display = 'none';
        document.getElementById('sendButton').disabled = false;
    }
    
    function clearChat() {
        if (confirm('هل أنت متأكد من مسح المحادثة؟')) {
            document.getElementById('chatMessages').innerHTML = `
                <div class='welcome-message'>
                    <i class='fas fa-robot'></i>
                    <h4>مرحباً مرة أخرى!</h4>
                    <p>كيف يمكنني مساعدتك اليوم؟</p>
                </div>
            `;
        }
    }
";

// تضمين الفوتر
include '../includes/footer.php';
?>

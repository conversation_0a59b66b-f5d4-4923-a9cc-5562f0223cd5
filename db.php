<?php
/**
 * ملف الاتصال بقاعدة البيانات
 * Database Connection File
 */

require_once 'config.php';

class Database {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $charset = DB_CHARSET;
    public $conn;

    // الاتصال بقاعدة البيانات
    public function getConnection() {
        $this->conn = null;
        
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $this->conn = new PDO($dsn, $this->username, $this->password);
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch(PDOException $exception) {
            echo "خطأ في الاتصال: " . $exception->getMessage();
        }
        
        return $this->conn;
    }

    // إنشاء قاعدة البيانات والجداول
    public function createDatabase() {
        try {
            // الاتصال بدون تحديد قاعدة البيانات
            $dsn = "mysql:host=" . $this->host . ";charset=" . $this->charset;
            $pdo = new PDO($dsn, $this->username, $this->password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // إنشاء قاعدة البيانات
            $sql = "CREATE DATABASE IF NOT EXISTS `" . $this->db_name . "` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
            $pdo->exec($sql);
            
            echo "تم إنشاء قاعدة البيانات بنجاح<br>";
            
            // الاتصال بقاعدة البيانات الجديدة
            $this->getConnection();
            
            // إنشاء الجداول
            $this->createTables();
            
        } catch(PDOException $e) {
            echo "خطأ في إنشاء قاعدة البيانات: " . $e->getMessage();
        }
    }

    // إنشاء الجداول
    private function createTables() {
        $tables = [
            // جدول المستخدمين
            "CREATE TABLE IF NOT EXISTS `users` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(100) NOT NULL,
                `email` varchar(100) UNIQUE NOT NULL,
                `password` varchar(255) NOT NULL,
                `role` enum('admin','parent','specialist','student') NOT NULL DEFAULT 'parent',
                `phone` varchar(20) DEFAULT NULL,
                `avatar` varchar(255) DEFAULT NULL,
                `is_active` tinyint(1) DEFAULT 1,
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

            // جدول التلاميذ
            "CREATE TABLE IF NOT EXISTS `students` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) NOT NULL,
                `parent_id` int(11) NOT NULL,
                `birth_date` date DEFAULT NULL,
                `diagnosis_level` enum('mild','moderate','severe') DEFAULT NULL,
                `interests` text DEFAULT NULL,
                `progress_level` int(11) DEFAULT 0,
                `weekly_stars` int(11) DEFAULT 0,
                `total_stars` int(11) DEFAULT 0,
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
                FOREIGN KEY (`parent_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

            // جدول المقالات
            "CREATE TABLE IF NOT EXISTS `articles` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `title` varchar(255) NOT NULL,
                `content` longtext NOT NULL,
                `author_id` int(11) NOT NULL,
                `category` varchar(100) DEFAULT NULL,
                `featured_image` varchar(255) DEFAULT NULL,
                `is_published` tinyint(1) DEFAULT 0,
                `views` int(11) DEFAULT 0,
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                FOREIGN KEY (`author_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

            // جدول الوسائط
            "CREATE TABLE IF NOT EXISTS `media` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `title` varchar(255) NOT NULL,
                `description` text DEFAULT NULL,
                `file_path` varchar(500) NOT NULL,
                `file_type` enum('image','audio','video','document','pdf') NOT NULL,
                `category` varchar(100) DEFAULT NULL,
                `uploaded_by` int(11) NOT NULL,
                `file_size` int(11) DEFAULT NULL,
                `downloads` int(11) DEFAULT 0,
                `is_public` tinyint(1) DEFAULT 1,
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                FOREIGN KEY (`uploaded_by`) REFERENCES `users`(`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

            // جدول التعليقات والاستشارات
            "CREATE TABLE IF NOT EXISTS `feedback` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) NOT NULL,
                `subject` varchar(255) NOT NULL,
                `message` text NOT NULL,
                `type` enum('question','consultation','feedback','complaint') DEFAULT 'question',
                `status` enum('pending','answered','closed') DEFAULT 'pending',
                `admin_response` text DEFAULT NULL,
                `responded_by` int(11) DEFAULT NULL,
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
                FOREIGN KEY (`responded_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

            // جدول الأخبار
            "CREATE TABLE IF NOT EXISTS `news` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `title` varchar(255) NOT NULL,
                `content` text NOT NULL,
                `is_active` tinyint(1) DEFAULT 1,
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

            // جدول الورشات
            "CREATE TABLE IF NOT EXISTS `workshops` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `title` varchar(255) NOT NULL,
                `description` text DEFAULT NULL,
                `date` datetime NOT NULL,
                `duration` int(11) DEFAULT NULL,
                `max_participants` int(11) DEFAULT NULL,
                `instructor_id` int(11) NOT NULL,
                `is_active` tinyint(1) DEFAULT 1,
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                FOREIGN KEY (`instructor_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
        ];

        foreach ($tables as $sql) {
            try {
                $this->conn->exec($sql);
                echo "تم إنشاء الجدول بنجاح<br>";
            } catch(PDOException $e) {
                echo "خطأ في إنشاء الجدول: " . $e->getMessage() . "<br>";
            }
        }
    }

    // إدراج بيانات تجريبية
    public function insertSampleData() {
        try {
            // إدراج مستخدم إداري
            $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
            $sql = "INSERT IGNORE INTO users (name, email, password, role) VALUES 
                    ('المدير العام', '<EMAIL>', :password, 'admin')";
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':password', $admin_password);
            $stmt->execute();

            // إدراج خبر تجريبي
            $sql = "INSERT IGNORE INTO news (title, content) VALUES 
                    ('مرحباً بكم في منصة نبراس', 'نرحب بكم في منصة نبراس التعليمية المتخصصة في دعم الأطفال ذوي اضطراب طيف التوحد')";
            $this->conn->exec($sql);

            echo "تم إدراج البيانات التجريبية بنجاح<br>";
        } catch(PDOException $e) {
            echo "خطأ في إدراج البيانات: " . $e->getMessage();
        }
    }
}

// إنشاء كائن قاعدة البيانات
$database = new Database();
$db = $database->getConnection();
?>

<?php
require_once 'config.php';
require_once 'db.php';

$error_message = '';
$success_message = '';

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $email = clean_input($_POST['email']);
    $password = $_POST['password'];
    
    if (empty($email) || empty($password)) {
        $error_message = 'يرجى إدخال البريد الإلكتروني وكلمة المرور';
    } else {
        try {
            $query = "SELECT * FROM users WHERE email = :email AND is_active = 1";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':email', $email);
            $stmt->execute();
            
            if ($stmt->rowCount() > 0) {
                $user = $stmt->fetch();
                
                if (password_verify($password, $user['password'])) {
                    // تسجيل الدخول بنجاح
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['user_name'] = $user['name'];
                    $_SESSION['user_email'] = $user['email'];
                    $_SESSION['user_role'] = $user['role'];
                    
                    // إعادة التوجيه حسب نوع المستخدم
                    switch ($user['role']) {
                        case 'admin':
                            redirect('admin/dashboard.php');
                            break;
                        case 'parent':
                            redirect('parent-space/dashboard.php');
                            break;
                        case 'specialist':
                            redirect('specialist-space/dashboard.php');
                            break;
                        case 'student':
                            redirect('student-space/dashboard.php');
                            break;
                        default:
                            redirect('index.php');
                    }
                } else {
                    $error_message = 'كلمة المرور غير صحيحة';
                }
            } else {
                $error_message = 'البريد الإلكتروني غير مسجل';
            }
        } catch (PDOException $e) {
            $error_message = 'حدث خطأ في النظام، يرجى المحاولة لاحقاً';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="style.css" rel="stylesheet">
</head>
<body>
    <!-- الشريط العلوي -->
    <header class="header">
        <div class="container">
            <a href="index.php" class="logo">
                <i class="fas fa-star"></i> نبراس
            </a>
            <nav>
                <ul class="nav-menu">
                    <li><a href="index.php">الرئيسية</a></li>
                    <li><a href="about.php">من نحن</a></li>
                    <li><a href="register.php" class="btn btn-secondary">إنشاء حساب</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- نموذج تسجيل الدخول -->
    <section class="section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-5">
                    <div class="card">
                        <div class="card-body p-4">
                            <div class="text-center mb-4">
                                <i class="fas fa-user-circle" style="font-size: 4rem; color: var(--primary-color);"></i>
                                <h2 class="mt-3">تسجيل الدخول</h2>
                                <p class="text-muted">أدخل بياناتك للوصول إلى حسابك</p>
                            </div>

                            <?php if (!empty($error_message)): ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <?php echo $error_message; ?>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($success_message)): ?>
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle"></i>
                                    <?php echo $success_message; ?>
                                </div>
                            <?php endif; ?>

                            <form method="POST" action="">
                                <div class="form-group mb-3">
                                    <label for="email" class="form-label">
                                        <i class="fas fa-envelope"></i> البريد الإلكتروني
                                    </label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" 
                                           required>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="password" class="form-label">
                                        <i class="fas fa-lock"></i> كلمة المرور
                                    </label>
                                    <div class="position-relative">
                                        <input type="password" class="form-control" id="password" name="password" required>
                                        <button type="button" class="btn btn-link position-absolute" 
                                                style="left: 10px; top: 50%; transform: translateY(-50%); border: none; background: none;"
                                                onclick="togglePassword()">
                                            <i class="fas fa-eye" id="toggleIcon"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="remember" name="remember">
                                        <label class="form-check-label" for="remember">
                                            تذكرني
                                        </label>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-primary w-100 mb-3">
                                    <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                                </button>
                            </form>

                            <div class="text-center">
                                <p class="mb-2">
                                    <a href="forgot-password.php" class="text-decoration-none">
                                        نسيت كلمة المرور؟
                                    </a>
                                </p>
                                <p>
                                    ليس لديك حساب؟ 
                                    <a href="register.php" class="text-decoration-none fw-bold">
                                        إنشاء حساب جديد
                                    </a>
                                </p>
                            </div>

                            <!-- بيانات تجريبية للاختبار -->
                            <div class="alert alert-info mt-4">
                                <h6><i class="fas fa-info-circle"></i> بيانات تجريبية للاختبار:</h6>
                                <small>
                                    <strong>المدير:</strong> <EMAIL> / admin123
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function togglePassword() {
            const passwordField = document.getElementById('password');
            const toggleIcon = document.getElementById('toggleIcon');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }
    </script>
</body>
</html>

<?php
/**
 * ملف تسجيل الخروج
 * Logout Handler
 */

require_once 'config.php';

// بدء الجلسة إذا لم تكن بدأت
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// تسجيل نشاط تسجيل الخروج
if (isset($_SESSION['user_id'])) {
    $user_id = $_SESSION['user_id'];
    $user_name = $_SESSION['user_name'] ?? 'مستخدم غير معروف';
    
    // يمكن إضافة تسجيل النشاط هنا
    $log_entry = date('Y-m-d H:i:s') . " - User ID: $user_id ($user_name) logged out\n";
    if (!file_exists('logs')) {
        mkdir('logs', 0777, true);
    }
    file_put_contents('logs/user_activities.log', $log_entry, FILE_APPEND | LOCK_EX);
}

// مسح جميع متغيرات الجلسة
$_SESSION = array();

// حذف ملف تعريف الارتباط للجلسة إذا كان موجوداً
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// تدمير الجلسة
session_destroy();

// إعادة التوجيه إلى الصفحة الرئيسية مع رسالة
header("Location: index.php?logged_out=1");
exit();
?>

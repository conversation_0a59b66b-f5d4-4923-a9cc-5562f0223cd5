<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كولي أمر
require_parent();

$user = current_user();
$students = get_parent_students($user['id']);

// جلب الاستشارات الخاصة بولي الأمر
$consultations_query = "SELECT * FROM feedback WHERE user_id = :user_id ORDER BY created_at DESC LIMIT 5";
$consultations_stmt = $db->prepare($consultations_query);
$consultations_stmt->bindParam(':user_id', $user['id']);
$consultations_stmt->execute();
$consultations = $consultations_stmt->fetchAll();

// جلب المقالات المفيدة للأولياء
$articles_query = "SELECT * FROM articles WHERE category IN ('نصائح للأولياء', 'تطوير المهارات', 'تعليمي') AND is_published = 1 ORDER BY created_at DESC LIMIT 4";
$articles_stmt = $db->prepare($articles_query);
$articles_stmt->execute();
$articles = $articles_stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فضاء الأولياء - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../style.css" rel="stylesheet">
    <link href="../rtl-enhancements.css" rel="stylesheet">
    
    <style>
        .parent-header {
            background: linear-gradient(135deg, #2c5aa0, #4a90e2);
            color: white;
            padding: 2rem 0;
            border-radius: 0 0 20px 20px;
        }
        
        .consultation-card {
            border-left: 4px solid var(--primary-color);
            transition: transform 0.3s ease;
        }
        
        .consultation-card:hover {
            transform: translateX(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .student-progress {
            background: linear-gradient(135deg, #4caf50, #8bc34a);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
        }
        
        .chat-widget {
            position: fixed;
            bottom: 20px;
            left: 20px;
            z-index: 1000;
        }
        
        .status-badge {
            font-size: 0.8rem;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
        }
    </style>
</head>
<body>
    <!-- الشريط العلوي -->
    <nav class="navbar navbar-expand-lg" style="background-color: var(--nibrass-blue);">
        <div class="container">
            <a class="navbar-brand text-white" href="../index.php">
                <i class="fas fa-star"></i> نبراس
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> <?php echo htmlspecialchars($user['name']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user"></i> الملف الشخصي</a></li>
                        <li><a class="dropdown-item" href="consultations.php"><i class="fas fa-comments"></i> الاستشارات</a></li>
                        <li><a class="dropdown-item" href="children.php"><i class="fas fa-child"></i> أطفالي</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- رأس فضاء الأولياء -->
    <section class="parent-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-user-friends"></i> مرحباً <?php echo htmlspecialchars($user['name']); ?></h1>
                    <p class="lead">نحن هنا لدعمك في رحلة تربية طفلك وتطوير مهاراته</p>
                </div>
                <div class="col-md-4 text-center">
                    <div class="d-flex justify-content-center gap-3">
                        <div class="text-center">
                            <i class="fas fa-child fa-2x mb-2"></i>
                            <h4><?php echo count($students); ?></h4>
                            <small>الأطفال</small>
                        </div>
                        <div class="text-center">
                            <i class="fas fa-comments fa-2x mb-2"></i>
                            <h4><?php echo count($consultations); ?></h4>
                            <small>الاستشارات</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- معلومات الأطفال -->
    <section class="section">
        <div class="container">
            <h2 class="section-title">أطفالي</h2>
            <?php if (!empty($students)): ?>
                <div class="row">
                    <?php foreach ($students as $student): ?>
                        <div class="col-lg-6 mb-4">
                            <div class="student-progress">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <div>
                                        <h5><i class="fas fa-child"></i> <?php echo htmlspecialchars($student['student_name']); ?></h5>
                                        <p class="mb-0">مستوى التقدم: <?php echo $student['progress_level']; ?>%</p>
                                    </div>
                                    <div class="text-center">
                                        <i class="fas fa-star fa-2x text-warning"></i>
                                        <div><?php echo $student['weekly_stars']; ?> نجمة</div>
                                    </div>
                                </div>
                                
                                <div class="progress mb-3" style="height: 8px;">
                                    <div class="progress-bar bg-warning" style="width: <?php echo $student['progress_level']; ?>%"></div>
                                </div>
                                
                                <div class="row text-center">
                                    <div class="col-4">
                                        <small>نجوم الأسبوع</small>
                                        <h6><?php echo $student['weekly_stars']; ?></h6>
                                    </div>
                                    <div class="col-4">
                                        <small>مجموع النجوم</small>
                                        <h6><?php echo $student['total_stars']; ?></h6>
                                    </div>
                                    <div class="col-4">
                                        <small>المستوى</small>
                                        <h6><?php echo $student['diagnosis_level'] ?? 'غير محدد'; ?></h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle"></i>
                    لم يتم ربط أي طفل بحسابك بعد. يرجى التواصل مع الإدارة لإضافة معلومات طفلك.
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- الخدمات السريعة -->
    <section class="section" style="background-color: #f8f9fa;">
        <div class="container">
            <h2 class="section-title">الخدمات السريعة</h2>
            <div class="row">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card text-center h-100">
                        <div class="card-body">
                            <i class="fas fa-question-circle fa-3x text-primary mb-3"></i>
                            <h5>طلب استشارة</h5>
                            <p>احصل على استشارة من المختصين</p>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#consultationModal">
                                طلب استشارة
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card text-center h-100">
                        <div class="card-body">
                            <i class="fas fa-robot fa-3x text-success mb-3"></i>
                            <h5>المساعد الذكي</h5>
                            <p>اسأل المساعد الذكي أي سؤال</p>
                            <button class="btn btn-success" onclick="openAIChat()">
                                بدء المحادثة
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card text-center h-100">
                        <div class="card-body">
                            <i class="fas fa-calendar-alt fa-3x text-warning mb-3"></i>
                            <h5>حجز ورشة</h5>
                            <p>احجز مكانك في الورشات التدريبية</p>
                            <a href="../workshops.php" class="btn btn-warning">
                                عرض الورشات
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card text-center h-100">
                        <div class="card-body">
                            <i class="fas fa-download fa-3x text-info mb-3"></i>
                            <h5>الموارد التعليمية</h5>
                            <p>حمل الأدلة والموارد المفيدة</p>
                            <a href="../library.php" class="btn btn-info">
                                تصفح المكتبة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- الاستشارات الأخيرة -->
    <section class="section">
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <h3><i class="fas fa-comments text-primary"></i> الاستشارات الأخيرة</h3>
                    <?php if (!empty($consultations)): ?>
                        <?php foreach ($consultations as $consultation): ?>
                            <div class="card consultation-card mb-3">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6><?php echo htmlspecialchars($consultation['subject']); ?></h6>
                                            <p class="text-muted small mb-2"><?php echo substr(htmlspecialchars($consultation['message']), 0, 100) . '...'; ?></p>
                                            <small class="text-muted">
                                                <i class="fas fa-clock"></i> <?php echo date('Y/m/d H:i', strtotime($consultation['created_at'])); ?>
                                            </small>
                                        </div>
                                        <div>
                                            <?php
                                            $status_class = '';
                                            $status_text = '';
                                            switch($consultation['status']) {
                                                case 'pending':
                                                    $status_class = 'bg-warning';
                                                    $status_text = 'في الانتظار';
                                                    break;
                                                case 'answered':
                                                    $status_class = 'bg-success';
                                                    $status_text = 'تم الرد';
                                                    break;
                                                case 'closed':
                                                    $status_class = 'bg-secondary';
                                                    $status_text = 'مغلق';
                                                    break;
                                            }
                                            ?>
                                            <span class="badge <?php echo $status_class; ?> status-badge"><?php echo $status_text; ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        <div class="text-center">
                            <a href="consultations.php" class="btn btn-outline-primary">عرض جميع الاستشارات</a>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            لم تقم بطلب أي استشارات بعد. يمكنك طلب استشارة من المختصين في أي وقت.
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="col-lg-4">
                    <h3><i class="fas fa-newspaper text-success"></i> مقالات مفيدة</h3>
                    <?php if (!empty($articles)): ?>
                        <?php foreach ($articles as $article): ?>
                            <div class="card mb-3">
                                <div class="card-body">
                                    <h6><a href="../article.php?id=<?php echo $article['id']; ?>" class="text-decoration-none">
                                        <?php echo htmlspecialchars($article['title']); ?>
                                    </a></h6>
                                    <p class="small text-muted"><?php echo substr(htmlspecialchars($article['content']), 0, 80) . '...'; ?></p>
                                    <span class="badge bg-info"><?php echo htmlspecialchars($article['category']); ?></span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            لا توجد مقالات متاحة حالياً.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>

    <!-- مودال طلب الاستشارة -->
    <div class="modal fade" id="consultationModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content modal-arabic">
                <div class="modal-header">
                    <h5 class="modal-title">طلب استشارة جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="submit_consultation.php">
                    <div class="modal-body">
                        <div class="form-group mb-3">
                            <label for="consultation_subject" class="form-label">موضوع الاستشارة</label>
                            <input type="text" class="form-control" id="consultation_subject" name="subject" required>
                        </div>
                        <div class="form-group mb-3">
                            <label for="consultation_type" class="form-label">نوع الاستشارة</label>
                            <select class="form-control" id="consultation_type" name="type" required>
                                <option value="consultation">استشارة عامة</option>
                                <option value="behavior">سلوك الطفل</option>
                                <option value="education">تعليمي</option>
                                <option value="social">مهارات اجتماعية</option>
                            </select>
                        </div>
                        <div class="form-group mb-3">
                            <label for="consultation_message" class="form-label">تفاصيل الاستشارة</label>
                            <textarea class="form-control" id="consultation_message" name="message" rows="5" required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">إرسال الاستشارة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- ويدجت الشات -->
    <div class="chat-widget">
        <button class="btn btn-success btn-lg rounded-circle" onclick="openAIChat()" title="المساعد الذكي">
            <i class="fas fa-robot"></i>
        </button>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function openAIChat() {
            // سيتم تطوير هذه الوظيفة لاحقاً لفتح شات الذكاء الاصطناعي
            alert('🤖 المساعد الذكي سيكون متاحاً قريباً! يمكنك في الوقت الحالي طلب استشارة من المختصين.');
        }
        
        // تأثيرات بصرية للبطاقات
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.transition = 'transform 0.3s ease';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>

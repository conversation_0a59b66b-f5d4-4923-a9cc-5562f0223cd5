<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كولي أمر
require_parent();

$user = current_user();
$students = get_parent_students($user['id']);

// جلب الاستشارات الخاصة بولي الأمر
$consultations_query = "SELECT c.*, s.name as specialist_name, s.specialization
                        FROM consultations c
                        LEFT JOIN users s ON c.specialist_id = s.id
                        WHERE c.parent_id = :user_id
                        ORDER BY c.created_at DESC LIMIT 5";
$consultations_stmt = $db->prepare($consultations_query);
$consultations_stmt->bindParam(':user_id', $user['id']);
$consultations_stmt->execute();
$consultations = $consultations_stmt->fetchAll();

// جلب المواعيد القادمة
$upcoming_appointments_query = "SELECT a.*, s.name as specialist_name
                               FROM appointments a
                               LEFT JOIN users s ON a.specialist_id = s.id
                               WHERE a.parent_id = :user_id AND a.appointment_date >= NOW()
                               ORDER BY a.appointment_date ASC LIMIT 3";
$appointments_stmt = $db->prepare($upcoming_appointments_query);
$appointments_stmt->bindParam(':user_id', $user['id']);
$appointments_stmt->execute();
$upcoming_appointments = $appointments_stmt->fetchAll();

// جلب الأنشطة الحديثة للأطفال
$recent_activities_query = "SELECT sa.*, s.name as student_name, sa.activity_title
                           FROM student_activities sa
                           LEFT JOIN students s ON sa.student_id = s.id
                           WHERE s.parent_id = :user_id
                           ORDER BY sa.completed_at DESC LIMIT 5";
$activities_stmt = $db->prepare($recent_activities_query);
$activities_stmt->bindParam(':user_id', $user['id']);
$activities_stmt->execute();
$recent_activities = $activities_stmt->fetchAll();

// جلب المقالات المفيدة للأولياء
$articles_query = "SELECT * FROM articles WHERE category IN ('نصائح للأولياء', 'تطوير المهارات', 'تعليمي') AND is_published = 1 ORDER BY created_at DESC LIMIT 4";
$articles_stmt = $db->prepare($articles_query);
$articles_stmt->execute();
$articles = $articles_stmt->fetchAll();

// إحصائيات سريعة
$total_students = count($students);
$total_consultations = count($consultations);
$pending_consultations = count(array_filter($consultations, function($c) { return $c['status'] == 'pending'; }));

// إعداد متغيرات الصفحة
$page_title = 'فضاء الأولياء - لوحة التحكم';
$page_description = 'لوحة تحكم شاملة لمتابعة تقدم أطفالكم والحصول على الدعم المناسب';

// تضمين الهيدر
include '../includes/header.php';
?>

<style>
/* Parent Dashboard Modern Styles */
.parent-dashboard-header {
    background: linear-gradient(135deg, #4a90e2, #2c5aa0, #1e3a8a, #3b82f6);
    background-size: 400% 400%;
    animation: gradientShift 8s ease infinite;
    color: white !important;
    padding: 3rem 0;
    border-radius: 0 0 30px 30px;
    position: relative;
    overflow: hidden;
}

.parent-dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><defs><pattern id="parentPattern" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100%" height="100%" fill="url(%23parentPattern)"/></svg>');
    opacity: 0.3;
}

.parent-dashboard-header h1,
.parent-dashboard-header p,
.parent-dashboard-header .lead {
    color: white !important;
    position: relative;
    z-index: 2;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.floating-shapes {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    pointer-events: none;
    z-index: 1;
}

.shape {
    position: absolute;
    color: rgba(255,255,255,0.1);
    animation: float 6s ease-in-out infinite;
}

.shape:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape:nth-child(2) {
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape:nth-child(3) {
    bottom: 30%;
    left: 20%;
    animation-delay: 4s;
}

.shape:nth-child(4) {
    top: 40%;
    right: 30%;
    animation-delay: 6s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* Dashboard Cards */
.dashboard-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    padding: 2rem;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
    border: none;
    position: relative;
    overflow: hidden;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--nibrass-green));
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.card-header-custom {
    display: flex;
    align-items: center;
    justify-content: between;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f8f9fa;
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-left: 1rem;
}

.card-icon.primary { background: linear-gradient(135deg, var(--primary-color), #2c5aa0); }
.card-icon.success { background: linear-gradient(135deg, var(--nibrass-green), #20c997); }
.card-icon.warning { background: linear-gradient(135deg, var(--nibrass-orange), #ff5722); }
.card-icon.info { background: linear-gradient(135deg, #17a2b8, #138496); }

.card-title-custom {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.card-subtitle {
    color: #666;
    font-size: 0.9rem;
    margin-top: 0.25rem;
}

/* Stats Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    border: none;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-color);
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.12);
}

.stat-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.stat-info h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 0;
}

.stat-info p {
    color: #666;
    margin: 0;
    font-weight: 500;
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.action-btn {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    padding: 1.5rem;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.action-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(74, 144, 226, 0.2);
}

.action-btn i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.action-btn span {
    font-weight: 600;
    font-size: 0.9rem;
}

/* List Items */
.list-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-radius: 10px;
    margin-bottom: 0.75rem;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.list-item:hover {
    background: #e3f2fd;
    transform: translateX(-5px);
}

.list-item-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 1rem;
    color: white;
    font-size: 0.9rem;
}

.list-item-content {
    flex: 1;
}

.list-item-title {
    font-weight: 600;
    color: #333;
    margin: 0 0 0.25rem 0;
    font-size: 0.95rem;
}

.list-item-subtitle {
    color: #666;
    font-size: 0.8rem;
    margin: 0;
}

.list-item-meta {
    color: #999;
    font-size: 0.75rem;
    text-align: left;
}

/* Status Badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-pending { background: #fff3cd; color: #856404; }
.status-confirmed { background: #d1ecf1; color: #0c5460; }
.status-completed { background: #d4edda; color: #155724; }
.status-cancelled { background: #f8d7da; color: #721c24; }

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #666;
}

.empty-state i {
    font-size: 3rem;
    color: #ddd;
    margin-bottom: 1rem;
}

.empty-state h4 {
    color: #999;
    margin-bottom: 0.5rem;
}

.empty-state p {
    color: #bbb;
    font-size: 0.9rem;
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 8px;
    height: 20px;
    margin-bottom: 10px;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .parent-dashboard-header {
        padding: 2rem 0;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .quick-actions {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .dashboard-card {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .card-header-custom {
        flex-direction: column;
        text-align: center;
    }

    .card-icon {
        margin-left: 0;
        margin-bottom: 1rem;
    }

    .stat-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .list-item {
        flex-direction: column;
        text-align: center;
        padding: 1.5rem;
    }

    .list-item-icon {
        margin-left: 0;
        margin-bottom: 1rem;
    }

    .list-item-meta {
        text-align: center;
        margin-top: 0.5rem;
    }
}

@media (max-width: 480px) {
    .quick-actions {
        grid-template-columns: 1fr;
    }

    .action-btn {
        padding: 1rem;
    }

    .dashboard-card {
        padding: 1rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .dashboard-card {
        background: #2d3748;
        color: #e2e8f0;
    }

    .card-title-custom {
        color: #e2e8f0;
    }

    .card-subtitle {
        color: #a0aec0;
    }

    .list-item {
        background: #4a5568;
        color: #e2e8f0;
    }

    .list-item-title {
        color: #e2e8f0;
    }

    .list-item-subtitle {
        color: #a0aec0;
    }
}
        }
    </style>

<!-- قسم العنوان المحسن -->
<section class="parent-dashboard-header position-relative">
    <div class="floating-shapes">
        <div class="shape"><i class="fas fa-user-friends fa-3x"></i></div>
        <div class="shape"><i class="fas fa-heart fa-3x"></i></div>
        <div class="shape"><i class="fas fa-comments fa-3x"></i></div>
        <div class="shape"><i class="fas fa-hands-helping fa-3x"></i></div>
    </div>
    <div class="container">
        <div class="hero-content text-center">
            <h1><i class="fas fa-user-friends"></i> مرحباً <?php echo htmlspecialchars($user['name']); ?></h1>
            <p class="lead">لوحة تحكم شاملة لمتابعة تقدم أطفالكم والحصول على الدعم المناسب</p>
            <div class="mt-4">
                <span class="badge bg-light text-dark fs-6 me-2">
                    <i class="fas fa-child"></i> <?php echo $total_students; ?> طفل
                </span>
                <span class="badge bg-light text-dark fs-6 me-2">
                    <i class="fas fa-comments"></i> <?php echo $total_consultations; ?> استشارة
                </span>
                <span class="badge bg-light text-dark fs-6 me-2">
                    <i class="fas fa-clock"></i> <?php echo count($upcoming_appointments); ?> موعد قادم
                </span>
                <span class="badge bg-light text-dark fs-6">
                    <i class="fas fa-chart-line"></i> متابعة مستمرة
                </span>
            </div>
        </div>
    </div>
</section>

<!-- محتوى لوحة التحكم -->
<section class="section">
    <div class="container">
        <!-- إحصائيات سريعة -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-content">
                    <div class="stat-info">
                        <h3><?php echo $total_students; ?></h3>
                        <p>الأطفال المسجلين</p>
                    </div>
                    <div class="stat-icon primary">
                        <i class="fas fa-child"></i>
                    </div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-content">
                    <div class="stat-info">
                        <h3><?php echo $total_consultations; ?></h3>
                        <p>إجمالي الاستشارات</p>
                    </div>
                    <div class="stat-icon success">
                        <i class="fas fa-comments"></i>
                    </div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-content">
                    <div class="stat-info">
                        <h3><?php echo $pending_consultations; ?></h3>
                        <p>استشارات معلقة</p>
                    </div>
                    <div class="stat-icon warning">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-content">
                    <div class="stat-info">
                        <h3><?php echo count($upcoming_appointments); ?></h3>
                        <p>مواعيد قادمة</p>
                    </div>
                    <div class="stat-icon info">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- إجراءات سريعة -->
        <div class="dashboard-card">
            <div class="card-header-custom">
                <div class="d-flex align-items-center">
                    <div class="card-icon primary">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <div>
                        <h3 class="card-title-custom">إجراءات سريعة</h3>
                        <p class="card-subtitle">الوصول السريع للخدمات الأساسية</p>
                    </div>
                </div>
            </div>

            <div class="quick-actions">
                <a href="consultation-request.php" class="action-btn">
                    <i class="fas fa-user-md"></i>
                    <span>طلب استشارة</span>
                </a>
                <a href="add_student.php" class="action-btn">
                    <i class="fas fa-user-plus"></i>
                    <span>إضافة طفل</span>
                </a>
                <a href="appointments.php" class="action-btn">
                    <i class="fas fa-calendar-alt"></i>
                    <span>المواعيد</span>
                </a>
                <a href="progress-reports.php" class="action-btn">
                    <i class="fas fa-chart-line"></i>
                    <span>تقارير التقدم</span>
                </a>
                <a href="ai-chat.php" class="action-btn">
                    <i class="fas fa-robot"></i>
                    <span>المساعد الذكي</span>
                </a>
                <a href="resources.php" class="action-btn">
                    <i class="fas fa-book-open"></i>
                    <span>الموارد التعليمية</span>
                </a>
            </div>
        </div>

        <div class="row">
            <!-- معلومات الأطفال -->
            <div class="col-lg-6">
                <div class="dashboard-card">
                    <div class="card-header-custom">
                        <div class="d-flex align-items-center">
                            <div class="card-icon success">
                                <i class="fas fa-child"></i>
                            </div>
                            <div>
                                <h3 class="card-title-custom">أطفالي</h3>
                                <p class="card-subtitle">معلومات وتقدم الأطفال المسجلين</p>
                            </div>
                        </div>
                    </div>

                    <?php if (!empty($students)): ?>
                        <?php foreach ($students as $student): ?>
                            <div class="list-item">
                                <div class="list-item-icon success">
                                    <i class="fas fa-child"></i>
                                </div>
                                <div class="list-item-content">
                                    <h5 class="list-item-title"><?php echo htmlspecialchars($student['name'] ?? $student['student_name']); ?></h5>
                                    <p class="list-item-subtitle">
                                        العمر: <?php echo $student['age'] ?? 'غير محدد'; ?> سنة |
                                        المستوى: <?php echo $student['diagnosis_level'] ?? 'غير محدد'; ?>
                                    </p>
                                    <div class="progress mt-2" style="height: 6px;">
                                        <div class="progress-bar bg-success" style="width: <?php echo $student['progress_level'] ?? 0; ?>%"></div>
                                    </div>
                                </div>
                                <div class="list-item-meta">
                                    <div class="text-center">
                                        <i class="fas fa-star text-warning"></i>
                                        <span><?php echo $student['weekly_stars'] ?? 0; ?></span>
                                    </div>
                                    <small>نجوم الأسبوع</small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="empty-state">
                            <i class="fas fa-child"></i>
                            <h4>لا توجد أطفال مسجلين</h4>
                            <p>ابدأ بإضافة طفلك الأول للاستفادة من خدمات المنصة</p>
                            <a href="add_student.php" class="btn btn-primary">
                                <i class="fas fa-plus"></i> إضافة طفل جديد
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- المواعيد القادمة -->
            <div class="col-lg-6">
                <div class="dashboard-card">
                    <div class="card-header-custom">
                        <div class="d-flex align-items-center">
                            <div class="card-icon info">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <div>
                                <h3 class="card-title-custom">المواعيد القادمة</h3>
                                <p class="card-subtitle">مواعيدك المجدولة مع الأخصائيين</p>
                            </div>
                        </div>
                    </div>

                    <?php if (!empty($upcoming_appointments)): ?>
                        <?php foreach ($upcoming_appointments as $appointment): ?>
                            <div class="list-item">
                                <div class="list-item-icon info">
                                    <i class="fas fa-calendar"></i>
                                </div>
                                <div class="list-item-content">
                                    <h5 class="list-item-title">د. <?php echo htmlspecialchars($appointment['specialist_name']); ?></h5>
                                    <p class="list-item-subtitle"><?php echo $appointment['appointment_type'] ?? 'استشارة عامة'; ?></p>
                                </div>
                                <div class="list-item-meta">
                                    <div><?php echo date('d/m/Y', strtotime($appointment['appointment_date'])); ?></div>
                                    <small><?php echo date('H:i', strtotime($appointment['appointment_date'])); ?></small>
                                    <span class="status-badge status-<?php echo $appointment['status']; ?>">
                                        <?php echo $appointment['status']; ?>
                                    </span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="empty-state">
                            <i class="fas fa-calendar-times"></i>
                            <h4>لا توجد مواعيد قادمة</h4>
                            <p>احجز موعدك مع أحد الأخصائيين</p>
                            <a href="consultation-request.php" class="btn btn-primary">
                                <i class="fas fa-plus"></i> حجز موعد
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- الاستشارات الحديثة -->
            <div class="col-lg-6">
                <div class="dashboard-card">
                    <div class="card-header-custom">
                        <div class="d-flex align-items-center">
                            <div class="card-icon warning">
                                <i class="fas fa-comments"></i>
                            </div>
                            <div>
                                <h3 class="card-title-custom">الاستشارات الحديثة</h3>
                                <p class="card-subtitle">آخر الاستشارات والمتابعات</p>
                            </div>
                        </div>
                    </div>

                    <?php if (!empty($consultations)): ?>
                        <?php foreach (array_slice($consultations, 0, 5) as $consultation): ?>
                            <div class="list-item">
                                <div class="list-item-icon warning">
                                    <i class="fas fa-comment"></i>
                                </div>
                                <div class="list-item-content">
                                    <h5 class="list-item-title"><?php echo htmlspecialchars($consultation['subject'] ?? 'استشارة عامة'); ?></h5>
                                    <p class="list-item-subtitle">
                                        د. <?php echo htmlspecialchars($consultation['specialist_name'] ?? 'غير محدد'); ?>
                                        <?php if ($consultation['specialization']): ?>
                                            - <?php echo htmlspecialchars($consultation['specialization']); ?>
                                        <?php endif; ?>
                                    </p>
                                </div>
                                <div class="list-item-meta">
                                    <div><?php echo date('d/m/Y', strtotime($consultation['created_at'])); ?></div>
                                    <span class="status-badge status-<?php echo $consultation['status']; ?>">
                                        <?php echo $consultation['status']; ?>
                                    </span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="empty-state">
                            <i class="fas fa-comments"></i>
                            <h4>لا توجد استشارات</h4>
                            <p>ابدأ بطلب استشارة من أحد الأخصائيين</p>
                            <a href="consultation-request.php" class="btn btn-primary">
                                <i class="fas fa-plus"></i> طلب استشارة
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- الأنشطة الحديثة -->
            <div class="col-lg-6">
                <div class="dashboard-card">
                    <div class="card-header-custom">
                        <div class="d-flex align-items-center">
                            <div class="card-icon success">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div>
                                <h3 class="card-title-custom">الأنشطة الحديثة</h3>
                                <p class="card-subtitle">آخر أنشطة الأطفال وإنجازاتهم</p>
                            </div>
                        </div>
                    </div>

                    <?php if (!empty($recent_activities)): ?>
                        <?php foreach (array_slice($recent_activities, 0, 5) as $activity): ?>
                            <div class="list-item">
                                <div class="list-item-icon success">
                                    <i class="fas fa-trophy"></i>
                                </div>
                                <div class="list-item-content">
                                    <h5 class="list-item-title"><?php echo htmlspecialchars($activity['activity_title'] ?? 'نشاط تعليمي'); ?></h5>
                                    <p class="list-item-subtitle">
                                        <?php echo htmlspecialchars($activity['student_name']); ?>
                                        <?php if ($activity['score']): ?>
                                            - النتيجة: <?php echo $activity['score']; ?>%
                                        <?php endif; ?>
                                    </p>
                                </div>
                                <div class="list-item-meta">
                                    <div><?php echo date('d/m/Y', strtotime($activity['completed_at'])); ?></div>
                                    <small><?php echo date('H:i', strtotime($activity['completed_at'])); ?></small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="empty-state">
                            <i class="fas fa-chart-line"></i>
                            <h4>لا توجد أنشطة حديثة</h4>
                            <p>ستظهر هنا أنشطة أطفالك وإنجازاتهم</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- مودال المساعد الذكي -->
<div class="modal fade" id="aiChatModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-robot"></i> المساعد الذكي
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="chatContainer" style="height: 400px; overflow-y: auto;">
                    <div class="text-center text-muted">
                        <i class="fas fa-robot fa-3x mb-3"></i>
                        <p>مرحباً! أنا المساعد الذكي لمنصة نبراس. كيف يمكنني مساعدتك اليوم؟</p>
                    </div>
                </div>
                <div class="input-group mt-3">
                    <input type="text" class="form-control" id="chatInput" placeholder="اكتب رسالتك هنا...">
                    <button class="btn btn-primary" onclick="sendMessage()">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// إضافة JavaScript خاص بالصفحة
$page_js = "
    function openAIChat() {
        const modal = new bootstrap.Modal(document.getElementById('aiChatModal'));
        modal.show();
    }

    function sendMessage() {
        const input = document.getElementById('chatInput');
        const message = input.value.trim();

        if (message) {
            // إضافة رسالة المستخدم
            addMessageToChat('user', message);
            input.value = '';

            // إرسال الرسالة للذكاء الاصطناعي
            sendToAI(message);
        }
    }

    function addMessageToChat(sender, message) {
        const container = document.getElementById('chatContainer');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'mb-3 ' + (sender === 'user' ? 'text-end' : 'text-start');

        messageDiv.innerHTML = `
            <div class='d-inline-block p-3 rounded' style='max-width: 70%; background: ${sender === 'user' ? 'var(--primary-color)' : '#f8f9fa'}; color: ${sender === 'user' ? 'white' : '#333'}'>
                ${message}
            </div>
        `;

        container.appendChild(messageDiv);
        container.scrollTop = container.scrollHeight;
    }

    function sendToAI(message) {
        // إضافة مؤشر الكتابة
        addMessageToChat('ai', '<i class=\"fas fa-spinner fa-spin\"></i> جاري الكتابة...');

        // محاكاة استجابة الذكاء الاصطناعي (سيتم تطويرها لاحقاً)
        setTimeout(() => {
            const container = document.getElementById('chatContainer');
            container.removeChild(container.lastChild);
            addMessageToChat('ai', 'شكراً لك على رسالتك. سيتم تطوير المساعد الذكي قريباً للإجابة على استفساراتك بشكل أفضل.');
        }, 2000);
    }

    // إرسال الرسالة عند الضغط على Enter
    document.getElementById('chatInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendMessage();
        }
    });
";

// تضمين الفوتر
include '../includes/footer.php';
?>

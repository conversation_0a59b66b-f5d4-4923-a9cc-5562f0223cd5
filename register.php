<?php
require_once 'config.php';
require_once 'db.php';

$error_message = '';
$success_message = '';

// معالجة التسجيل
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $name = clean_input($_POST['name']);
    $email = clean_input($_POST['email']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $role = clean_input($_POST['role']);
    $phone = clean_input($_POST['phone']);
    
    // التحقق من البيانات
    if (empty($name) || empty($email) || empty($password) || empty($role)) {
        $error_message = 'يرجى ملء جميع الحقول المطلوبة';
    } elseif ($password !== $confirm_password) {
        $error_message = 'كلمة المرور وتأكيد كلمة المرور غير متطابقتين';
    } elseif (strlen($password) < 6) {
        $error_message = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    } else {
        try {
            // التحقق من عدم وجود البريد الإلكتروني مسبقاً
            $check_query = "SELECT id FROM users WHERE email = :email";
            $check_stmt = $db->prepare($check_query);
            $check_stmt->bindParam(':email', $email);
            $check_stmt->execute();
            
            if ($check_stmt->rowCount() > 0) {
                $error_message = 'البريد الإلكتروني مسجل مسبقاً';
            } else {
                // إنشاء الحساب الجديد
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                
                $insert_query = "INSERT INTO users (name, email, password, role, phone) VALUES (:name, :email, :password, :role, :phone)";
                $insert_stmt = $db->prepare($insert_query);
                $insert_stmt->bindParam(':name', $name);
                $insert_stmt->bindParam(':email', $email);
                $insert_stmt->bindParam(':password', $hashed_password);
                $insert_stmt->bindParam(':role', $role);
                $insert_stmt->bindParam(':phone', $phone);
                
                if ($insert_stmt->execute()) {
                    $success_message = 'تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول';
                    // مسح البيانات من النموذج
                    $_POST = array();
                } else {
                    $error_message = 'حدث خطأ أثناء إنشاء الحساب';
                }
            }
        } catch (PDOException $e) {
            $error_message = 'حدث خطأ في النظام، يرجى المحاولة لاحقاً';
        }
    }
}
?>

<?php
// إعداد متغيرات الصفحة
$page_title = 'إنشاء حساب جديد';
$page_description = 'انضم إلى منصة نبراس وابدأ رحلتك التعليمية';

// تضمين الهيدر
include 'includes/header.php';
?>

<style>
.register-header {
    background: linear-gradient(135deg, #4a90e2, #2c5aa0, #1e3a8a, #3b82f6);
    background-size: 400% 400%;
    animation: gradientShift 8s ease infinite;
    color: white !important;
    padding: 3rem 0;
    border-radius: 0 0 30px 30px;
}

.register-header h1,
.register-header p,
.register-header .lead {
    color: white !important;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.floating-shapes {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    pointer-events: none;
}

.shape {
    position: absolute;
    color: rgba(255,255,255,0.1);
    animation: float 6s ease-in-out infinite;
}

.shape:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape:nth-child(2) {
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape:nth-child(3) {
    bottom: 30%;
    left: 20%;
    animation-delay: 4s;
}

.shape:nth-child(4) {
    top: 40%;
    right: 30%;
    animation-delay: 6s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.register-form-container {
    background: white;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-top: 2rem;
    position: relative;
    z-index: 10;
}

.register-form-header {
    background: linear-gradient(135deg, var(--primary-color), var(--nibrass-green));
    color: white;
    padding: 2rem;
    text-align: center;
}

.register-form-body {
    padding: 2rem;
}

.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--nibrass-blue);
    margin-bottom: 0.5rem;
}

.btn-register {
    background: linear-gradient(135deg, var(--primary-color), var(--nibrass-green));
    border: none;
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-register:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(74, 144, 226, 0.3);
}

.role-card {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.role-card:hover {
    border-color: var(--primary-color);
    background-color: #f8f9fa;
}

.role-card.selected {
    border-color: var(--primary-color);
    background-color: rgba(74, 144, 226, 0.1);
}

.role-card input[type="radio"] {
    display: none;
}

.role-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}
</style>

<!-- قسم العنوان المحسن -->
<section class="register-header position-relative">
    <div class="floating-shapes">
        <div class="shape"><i class="fas fa-user-plus fa-3x"></i></div>
        <div class="shape"><i class="fas fa-graduation-cap fa-3x"></i></div>
        <div class="shape"><i class="fas fa-heart fa-3x"></i></div>
        <div class="shape"><i class="fas fa-star fa-3x"></i></div>
    </div>
    <div class="container">
        <div class="hero-content text-center">
            <h1><i class="fas fa-user-plus"></i> إنشاء حساب جديد</h1>
            <p class="lead">انضم إلى منصة نبراس وابدأ رحلتك التعليمية</p>
            <div class="mt-4">
                <span class="badge bg-light text-dark fs-6 me-2">
                    <i class="fas fa-child"></i> للتلاميذ
                </span>
                <span class="badge bg-light text-dark fs-6 me-2">
                    <i class="fas fa-users"></i> للأولياء
                </span>
                <span class="badge bg-light text-dark fs-6 me-2">
                    <i class="fas fa-user-md"></i> للأخصائيين
                </span>
                <span class="badge bg-light text-dark fs-6">
                    <i class="fas fa-shield-alt"></i> آمن
                </span>
            </div>
        </div>
    </div>
</section>

    <!-- نموذج التسجيل المحسن -->
    <section class="section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-10 col-lg-8">
                    <div class="register-form-container">
                        <div class="register-form-body">

                            <?php if (!empty($error_message)): ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <?php echo $error_message; ?>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($success_message)): ?>
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle"></i>
                                    <?php echo $success_message; ?>
                                    <div class="mt-2">
                                        <a href="login.php" class="btn btn-success btn-sm">تسجيل الدخول الآن</a>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <form method="POST" action="">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="name" class="form-label">
                                                <i class="fas fa-user"></i> الاسم الكامل *
                                            </label>
                                            <input type="text" class="form-control" id="name" name="name" 
                                                   value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>" 
                                                   required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="email" class="form-label">
                                                <i class="fas fa-envelope"></i> البريد الإلكتروني *
                                            </label>
                                            <input type="email" class="form-control" id="email" name="email" 
                                                   value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" 
                                                   required>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="password" class="form-label">
                                                <i class="fas fa-lock"></i> كلمة المرور *
                                            </label>
                                            <input type="password" class="form-control" id="password" name="password" 
                                                   minlength="6" required>
                                            <small class="text-muted">6 أحرف على الأقل</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="confirm_password" class="form-label">
                                                <i class="fas fa-lock"></i> تأكيد كلمة المرور *
                                            </label>
                                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                                   minlength="6" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="role" class="form-label">
                                                <i class="fas fa-user-tag"></i> نوع الحساب *
                                            </label>
                                            <select class="form-control" id="role" name="role" required>
                                                <option value="">اختر نوع الحساب</option>
                                                <option value="parent" <?php echo (isset($_POST['role']) && $_POST['role'] == 'parent') ? 'selected' : ''; ?>>
                                                    ولي أمر
                                                </option>
                                                <option value="specialist" <?php echo (isset($_POST['role']) && $_POST['role'] == 'specialist') ? 'selected' : ''; ?>>
                                                    أخصائي
                                                </option>
                                                <option value="student" <?php echo (isset($_POST['role']) && $_POST['role'] == 'student') ? 'selected' : ''; ?>>
                                                    طالب
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="phone" class="form-label">
                                                <i class="fas fa-phone"></i> رقم الهاتف
                                            </label>
                                            <input type="tel" class="form-control" id="phone" name="phone" 
                                                   value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                                        <label class="form-check-label" for="terms">
                                            أوافق على <a href="terms.php" target="_blank">شروط الاستخدام</a> و 
                                            <a href="privacy.php" target="_blank">سياسة الخصوصية</a>
                                        </label>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-primary w-100 mb-3">
                                    <i class="fas fa-user-plus"></i> إنشاء الحساب
                                </button>
                            </form>

                            <div class="text-center">
                                <p>
                                    لديك حساب بالفعل؟ 
                                    <a href="login.php" class="text-decoration-none fw-bold">
                                        تسجيل الدخول
                                    </a>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

<?php
// إضافة JavaScript خاص بالصفحة
$page_js = "
    // التحقق من تطابق كلمة المرور
    document.getElementById('confirm_password').addEventListener('input', function() {
        const password = document.getElementById('password').value;
        const confirmPassword = this.value;

        if (password !== confirmPassword) {
            this.setCustomValidity('كلمة المرور غير متطابقة');
        } else {
            this.setCustomValidity('');
        }
    });

    // تحسين تجربة المستخدم
    document.querySelectorAll('.role-card').forEach(card => {
        card.addEventListener('click', function() {
            document.querySelectorAll('.role-card').forEach(c => c.classList.remove('selected'));
            this.classList.add('selected');
            this.querySelector('input[type=\"radio\"]').checked = true;
        });
    });
";

// تضمين الفوتر
include 'includes/footer.php';
?>

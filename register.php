<?php
require_once 'config.php';
require_once 'db.php';

$error_message = '';
$success_message = '';

// معالجة التسجيل
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $name = clean_input($_POST['name']);
    $email = clean_input($_POST['email']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $role = clean_input($_POST['role']);
    $phone = clean_input($_POST['phone']);
    
    // التحقق من البيانات
    if (empty($name) || empty($email) || empty($password) || empty($role)) {
        $error_message = 'يرجى ملء جميع الحقول المطلوبة';
    } elseif ($password !== $confirm_password) {
        $error_message = 'كلمة المرور وتأكيد كلمة المرور غير متطابقتين';
    } elseif (strlen($password) < 6) {
        $error_message = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    } else {
        try {
            // التحقق من عدم وجود البريد الإلكتروني مسبقاً
            $check_query = "SELECT id FROM users WHERE email = :email";
            $check_stmt = $db->prepare($check_query);
            $check_stmt->bindParam(':email', $email);
            $check_stmt->execute();
            
            if ($check_stmt->rowCount() > 0) {
                $error_message = 'البريد الإلكتروني مسجل مسبقاً';
            } else {
                // إنشاء الحساب الجديد
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                
                $insert_query = "INSERT INTO users (name, email, password, role, phone) VALUES (:name, :email, :password, :role, :phone)";
                $insert_stmt = $db->prepare($insert_query);
                $insert_stmt->bindParam(':name', $name);
                $insert_stmt->bindParam(':email', $email);
                $insert_stmt->bindParam(':password', $hashed_password);
                $insert_stmt->bindParam(':role', $role);
                $insert_stmt->bindParam(':phone', $phone);
                
                if ($insert_stmt->execute()) {
                    $success_message = 'تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول';
                    // مسح البيانات من النموذج
                    $_POST = array();
                } else {
                    $error_message = 'حدث خطأ أثناء إنشاء الحساب';
                }
            }
        } catch (PDOException $e) {
            $error_message = 'حدث خطأ في النظام، يرجى المحاولة لاحقاً';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="style.css" rel="stylesheet">
</head>
<body>
    <!-- الشريط العلوي -->
    <header class="header">
        <div class="container">
            <a href="index.php" class="logo">
                <i class="fas fa-star"></i> نبراس
            </a>
            <nav>
                <ul class="nav-menu">
                    <li><a href="index.php">الرئيسية</a></li>
                    <li><a href="about.php">من نحن</a></li>
                    <li><a href="login.php" class="btn btn-secondary">تسجيل الدخول</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- نموذج التسجيل -->
    <section class="section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6">
                    <div class="card">
                        <div class="card-body p-4">
                            <div class="text-center mb-4">
                                <i class="fas fa-user-plus" style="font-size: 4rem; color: var(--primary-color);"></i>
                                <h2 class="mt-3">إنشاء حساب جديد</h2>
                                <p class="text-muted">انضم إلى منصة نبراس وابدأ رحلتك التعليمية</p>
                            </div>

                            <?php if (!empty($error_message)): ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <?php echo $error_message; ?>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($success_message)): ?>
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle"></i>
                                    <?php echo $success_message; ?>
                                    <div class="mt-2">
                                        <a href="login.php" class="btn btn-success btn-sm">تسجيل الدخول الآن</a>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <form method="POST" action="">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="name" class="form-label">
                                                <i class="fas fa-user"></i> الاسم الكامل *
                                            </label>
                                            <input type="text" class="form-control" id="name" name="name" 
                                                   value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>" 
                                                   required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="email" class="form-label">
                                                <i class="fas fa-envelope"></i> البريد الإلكتروني *
                                            </label>
                                            <input type="email" class="form-control" id="email" name="email" 
                                                   value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" 
                                                   required>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="password" class="form-label">
                                                <i class="fas fa-lock"></i> كلمة المرور *
                                            </label>
                                            <input type="password" class="form-control" id="password" name="password" 
                                                   minlength="6" required>
                                            <small class="text-muted">6 أحرف على الأقل</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="confirm_password" class="form-label">
                                                <i class="fas fa-lock"></i> تأكيد كلمة المرور *
                                            </label>
                                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                                   minlength="6" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="role" class="form-label">
                                                <i class="fas fa-user-tag"></i> نوع الحساب *
                                            </label>
                                            <select class="form-control" id="role" name="role" required>
                                                <option value="">اختر نوع الحساب</option>
                                                <option value="parent" <?php echo (isset($_POST['role']) && $_POST['role'] == 'parent') ? 'selected' : ''; ?>>
                                                    ولي أمر
                                                </option>
                                                <option value="specialist" <?php echo (isset($_POST['role']) && $_POST['role'] == 'specialist') ? 'selected' : ''; ?>>
                                                    أخصائي
                                                </option>
                                                <option value="student" <?php echo (isset($_POST['role']) && $_POST['role'] == 'student') ? 'selected' : ''; ?>>
                                                    طالب
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="phone" class="form-label">
                                                <i class="fas fa-phone"></i> رقم الهاتف
                                            </label>
                                            <input type="tel" class="form-control" id="phone" name="phone" 
                                                   value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                                        <label class="form-check-label" for="terms">
                                            أوافق على <a href="terms.php" target="_blank">شروط الاستخدام</a> و 
                                            <a href="privacy.php" target="_blank">سياسة الخصوصية</a>
                                        </label>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-primary w-100 mb-3">
                                    <i class="fas fa-user-plus"></i> إنشاء الحساب
                                </button>
                            </form>

                            <div class="text-center">
                                <p>
                                    لديك حساب بالفعل؟ 
                                    <a href="login.php" class="text-decoration-none fw-bold">
                                        تسجيل الدخول
                                    </a>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // التحقق من تطابق كلمة المرور
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (password !== confirmPassword) {
                this.setCustomValidity('كلمة المرور غير متطابقة');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>

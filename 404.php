<?php
require_once 'config.php';

// إعداد متغيرات الصفحة
$page_title = 'الصفحة غير موجودة - 404';
$page_description = 'الصفحة التي تبحث عنها غير موجودة';

// تعيين رمز الاستجابة HTTP
http_response_code(404);

// تضمين الهيدر
include 'includes/header.php';
?>

<!-- صفحة 404 -->
<section class="section" style="min-height: 70vh; display: flex; align-items: center;">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-6">
                <div class="error-content">
                    <!-- رقم الخطأ -->
                    <h1 class="error-number" style="font-size: 8rem; font-weight: 700; color: var(--primary-color); margin-bottom: 1rem;">
                        404
                    </h1>
                    
                    <!-- رسالة الخطأ -->
                    <h2 style="color: var(--nibrass-blue); margin-bottom: 1.5rem;">
                        <i class="fas fa-search"></i>
                        الصفحة غير موجودة
                    </h2>
                    
                    <p class="lead" style="color: #666; margin-bottom: 2rem;">
                        عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى مكان آخر.
                        يمكنك العودة إلى الصفحة الرئيسية أو استخدام القائمة للتنقل.
                    </p>
                    
                    <!-- أزرار التنقل -->
                    <div class="error-actions">
                        <a href="<?php echo get_url('index.php'); ?>" class="btn btn-primary btn-lg me-3">
                            <i class="fas fa-home"></i>
                            العودة للرئيسية
                        </a>
                        <a href="javascript:history.back()" class="btn btn-secondary btn-lg">
                            <i class="fas fa-arrow-right"></i>
                            الصفحة السابقة
                        </a>
                    </div>
                    
                    <!-- روابط مفيدة -->
                    <div class="helpful-links mt-5">
                        <h5 style="color: var(--nibrass-blue); margin-bottom: 1rem;">
                            روابط مفيدة:
                        </h5>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <a href="<?php echo get_url('about.php'); ?>" class="btn btn-outline-primary btn-block">
                                    <i class="fas fa-info-circle"></i>
                                    من نحن
                                </a>
                            </div>
                            <div class="col-md-6 mb-3">
                                <a href="<?php echo get_url('library.php'); ?>" class="btn btn-outline-success btn-block">
                                    <i class="fas fa-book"></i>
                                    المكتبة
                                </a>
                            </div>
                            <div class="col-md-6 mb-3">
                                <a href="<?php echo get_url('workshops.php'); ?>" class="btn btn-outline-warning btn-block">
                                    <i class="fas fa-chalkboard-teacher"></i>
                                    الورشات
                                </a>
                            </div>
                            <div class="col-md-6 mb-3">
                                <a href="<?php echo get_url('contact.php'); ?>" class="btn btn-outline-info btn-block">
                                    <i class="fas fa-envelope"></i>
                                    اتصل بنا
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- رسم توضيحي -->
            <div class="col-lg-6">
                <div class="error-illustration">
                    <div style="font-size: 15rem; color: #e9ecef; margin-bottom: 2rem;">
                        <i class="fas fa-search"></i>
                    </div>
                    
                    <!-- إحصائيات سريعة -->
                    <div class="quick-stats">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="stat-card p-3 bg-light rounded">
                                    <h4 class="text-primary">500+</h4>
                                    <small>مستفيد</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-card p-3 bg-light rounded">
                                    <h4 class="text-success">100+</h4>
                                    <small>مورد</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-card p-3 bg-light rounded">
                                    <h4 class="text-warning">50+</h4>
                                    <small>ورشة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- قسم البحث -->
<section class="section" style="background-color: #f8f9fa;">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="search-section text-center">
                    <h3 style="color: var(--nibrass-blue); margin-bottom: 1.5rem;">
                        <i class="fas fa-search"></i>
                        ابحث عما تريد
                    </h3>
                    
                    <form class="search-form" action="<?php echo get_url('search.php'); ?>" method="GET">
                        <div class="input-group input-group-lg">
                            <input type="text" class="form-control" name="q" placeholder="ابحث في المنصة..." required>
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i>
                                بحث
                            </button>
                        </div>
                    </form>
                    
                    <div class="search-suggestions mt-3">
                        <small class="text-muted">اقتراحات البحث:</small>
                        <div class="mt-2">
                            <a href="<?php echo get_url('search.php?q=التوحد'); ?>" class="badge bg-secondary me-2">التوحد</a>
                            <a href="<?php echo get_url('search.php?q=الأطفال'); ?>" class="badge bg-secondary me-2">الأطفال</a>
                            <a href="<?php echo get_url('search.php?q=التعليم'); ?>" class="badge bg-secondary me-2">التعليم</a>
                            <a href="<?php echo get_url('search.php?q=الأولياء'); ?>" class="badge bg-secondary me-2">الأولياء</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.error-number {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.error-actions .btn {
    margin: 0.5rem;
    transition: all 0.3s ease;
}

.error-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.helpful-links .btn {
    width: 100%;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.helpful-links .btn:hover {
    transform: translateX(-5px);
}

.stat-card {
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.search-form .input-group {
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    border-radius: 50px;
    overflow: hidden;
}

.search-form .form-control {
    border: none;
    padding: 1rem 1.5rem;
}

.search-form .btn {
    border: none;
    padding: 1rem 2rem;
}

.search-suggestions .badge {
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-suggestions .badge:hover {
    transform: scale(1.1);
}
</style>

<?php
// تضمين الفوتر
include 'includes/footer.php';
?>

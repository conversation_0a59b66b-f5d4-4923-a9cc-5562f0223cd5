<?php
require_once 'config.php';
require_once 'db.php';

// إعداد متغيرات الصفحة
$page_title = 'قسم الإبداع';
$page_description = 'قسم الإبداع والأنشطة الفنية للأطفال ذوي التوحد';

// جلب الأعمال الإبداعية
$creative_works_query = "SELECT * FROM media WHERE category IN ('رسومات', 'أناشيد', 'فيديوهات إبداعية') AND is_public = 1 ORDER BY created_at DESC";
$creative_stmt = $db->prepare($creative_works_query);
$creative_stmt->execute();
$creative_works = $creative_stmt->fetchAll();
?>
    
    <style>
        .creativity-header {
            background: linear-gradient(135deg, #4a90e2, #2c5aa0, #1e3a8a, #3b82f6);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
            color: white !important;
            padding: 3rem 0;
            border-radius: 0 0 30px 30px;
        }

        .creativity-header h1,
        .creativity-header p,
        .creativity-header .lead {
            color: white !important;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .creative-card {
            border: none;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            position: relative;
        }
        
        .creative-card:hover {
            transform: translateY(-15px) rotate(2deg);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }
        
        .creative-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #ff9800, #4caf50, #2196f3, #9c27b0);
        }
        
        .activity-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff9800, #ff5722);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .gallery-item {
            position: relative;
            overflow: hidden;
            border-radius: 15px;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .gallery-item:hover {
            transform: scale(1.05);
        }
        
        .gallery-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,152,0,0.8), rgba(76,175,80,0.8));
            opacity: 0;
            transition: opacity 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
        }
        
        .gallery-item:hover .gallery-overlay {
            opacity: 1;
        }
        
        .creative-stats {
            background: linear-gradient(135deg, #4caf50, #8bc34a);
            color: white;
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
        }
        
        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }
        
        .shape {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }
        
        .shape:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
        .shape:nth-child(2) { top: 20%; right: 10%; animation-delay: 2s; }
        .shape:nth-child(3) { bottom: 20%; left: 20%; animation-delay: 4s; }
        .shape:nth-child(4) { bottom: 10%; right: 20%; animation-delay: 1s; }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
    
    <!-- تضمين الهيدر -->
    <?php include 'includes/header.php'; ?>

    <!-- قسم العنوان -->
    <section class="creativity-header position-relative">
        <div class="floating-shapes">
            <div class="shape"><i class="fas fa-palette fa-3x"></i></div>
            <div class="shape"><i class="fas fa-music fa-3x"></i></div>
            <div class="shape"><i class="fas fa-paint-brush fa-3x"></i></div>
            <div class="shape"><i class="fas fa-camera fa-3x"></i></div>
        </div>
        <div class="container">
            <div class="hero-content text-center">
                <h1><i class="fas fa-rainbow"></i> قسم الإبداع والفنون</h1>
                <p class="lead">اكتشف عالم الإبداع والفن مع أطفالنا المبدعين</p>
                <div class="mt-4">
                    <span class="badge bg-light text-dark fs-6 me-2">
                        <i class="fas fa-palette"></i> رسومات
                    </span>
                    <span class="badge bg-light text-dark fs-6 me-2">
                        <i class="fas fa-music"></i> أناشيد
                    </span>
                    <span class="badge bg-light text-dark fs-6">
                        <i class="fas fa-video"></i> فيديوهات
                    </span>
                </div>
            </div>
        </div>
    </section>

    <!-- الأنشطة الإبداعية -->
    <section class="section">
        <div class="container">
            <h2 class="section-title">الأنشطة الإبداعية</h2>
            <div class="row">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card creative-card text-center h-100">
                        <div class="card-body">
                            <div class="activity-icon">
                                <i class="fas fa-palette"></i>
                            </div>
                            <h5>الرسم والتلوين</h5>
                            <p>أنشطة رسم وتلوين تحفز الإبداع وتطور المهارات الحركية الدقيقة</p>
                            <button class="btn btn-primary" onclick="startActivity('drawing')">
                                <i class="fas fa-play"></i> ابدأ الرسم
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card creative-card text-center h-100">
                        <div class="card-body">
                            <div class="activity-icon">
                                <i class="fas fa-music"></i>
                            </div>
                            <h5>الموسيقى والأناشيد</h5>
                            <p>أناشيد تعليمية وموسيقى هادئة تساعد على التعلم والاسترخاء</p>
                            <button class="btn btn-success" onclick="startActivity('music')">
                                <i class="fas fa-headphones"></i> استمع الآن
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card creative-card text-center h-100">
                        <div class="card-body">
                            <div class="activity-icon">
                                <i class="fas fa-video"></i>
                            </div>
                            <h5>الفيديوهات الإبداعية</h5>
                            <p>فيديوهات تعليمية وترفيهية تجمع بين المتعة والتعلم</p>
                            <button class="btn btn-warning" onclick="startActivity('videos')">
                                <i class="fas fa-play-circle"></i> شاهد الآن
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card creative-card text-center h-100">
                        <div class="card-body">
                            <div class="activity-icon">
                                <i class="fas fa-camera"></i>
                            </div>
                            <h5>التصوير والذكريات</h5>
                            <p>مشاركة اللحظات الجميلة والذكريات الخاصة مع المجتمع</p>
                            <button class="btn btn-info" onclick="startActivity('photography')">
                                <i class="fas fa-camera-retro"></i> شارك صورة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- معرض الأعمال -->
    <section class="section" style="background-color: #f8f9fa;">
        <div class="container">
            <h2 class="section-title">معرض أعمال أطفالنا</h2>
            <div class="row">
                <!-- رسومات الأطفال -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="gallery-item">
                        <img src="https://via.placeholder.com/300x200/ff9800/white?text=رسمة+أحمد" class="img-fluid" alt="رسمة أحمد">
                        <div class="gallery-overlay">
                            <i class="fas fa-eye"></i>
                        </div>
                    </div>
                    <div class="text-center mt-2">
                        <h6>رسمة العائلة السعيدة</h6>
                        <small class="text-muted">بواسطة أحمد - 8 سنوات</small>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="gallery-item">
                        <img src="https://via.placeholder.com/300x200/4caf50/white?text=رسمة+فاطمة" class="img-fluid" alt="رسمة فاطمة">
                        <div class="gallery-overlay">
                            <i class="fas fa-eye"></i>
                        </div>
                    </div>
                    <div class="text-center mt-2">
                        <h6>حديقة الألوان</h6>
                        <small class="text-muted">بواسطة فاطمة - 7 سنوات</small>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="gallery-item">
                        <img src="https://via.placeholder.com/300x200/2196f3/white?text=رسمة+محمد" class="img-fluid" alt="رسمة محمد">
                        <div class="gallery-overlay">
                            <i class="fas fa-eye"></i>
                        </div>
                    </div>
                    <div class="text-center mt-2">
                        <h6>البحر والسمك</h6>
                        <small class="text-muted">بواسطة محمد - 9 سنوات</small>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="gallery-item">
                        <img src="https://via.placeholder.com/300x200/9c27b0/white?text=رسمة+سارة" class="img-fluid" alt="رسمة سارة">
                        <div class="gallery-overlay">
                            <i class="fas fa-eye"></i>
                        </div>
                    </div>
                    <div class="text-center mt-2">
                        <h6>قوس قزح الأحلام</h6>
                        <small class="text-muted">بواسطة سارة - 6 سنوات</small>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="gallery-item">
                        <img src="https://via.placeholder.com/300x200/ff5722/white?text=رسمة+علي" class="img-fluid" alt="رسمة علي">
                        <div class="gallery-overlay">
                            <i class="fas fa-eye"></i>
                        </div>
                    </div>
                    <div class="text-center mt-2">
                        <h6>الفضاء والنجوم</h6>
                        <small class="text-muted">بواسطة علي - 10 سنوات</small>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="gallery-item">
                        <img src="https://via.placeholder.com/300x200/795548/white?text=رسمة+نور" class="img-fluid" alt="رسمة نور">
                        <div class="gallery-overlay">
                            <i class="fas fa-eye"></i>
                        </div>
                    </div>
                    <div class="text-center mt-2">
                        <h6>بيتي الجميل</h6>
                        <small class="text-muted">بواسطة نور - 8 سنوات</small>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- إحصائيات الإبداع -->
    <section class="section">
        <div class="container">
            <div class="row">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="creative-stats">
                        <i class="fas fa-palette fa-3x mb-3"></i>
                        <h3>150+</h3>
                        <p>رسمة إبداعية</p>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="creative-stats" style="background: linear-gradient(135deg, #2196f3, #03a9f4);">
                        <i class="fas fa-music fa-3x mb-3"></i>
                        <h3>50+</h3>
                        <p>أنشودة تعليمية</p>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="creative-stats" style="background: linear-gradient(135deg, #ff9800, #ff5722);">
                        <i class="fas fa-video fa-3x mb-3"></i>
                        <h3>30+</h3>
                        <p>فيديو إبداعي</p>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="creative-stats" style="background: linear-gradient(135deg, #9c27b0, #e91e63);">
                        <i class="fas fa-child fa-3x mb-3"></i>
                        <h3>200+</h3>
                        <p>طفل مبدع</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- دعوة للمشاركة -->
    <section class="section" style="background: linear-gradient(135deg, #4caf50, #8bc34a); color: white;">
        <div class="container text-center">
            <h2>شارك إبداع طفلك معنا!</h2>
            <p class="lead">نحن نحب أن نرى إبداعات أطفالنا الرائعة</p>
            <div class="mt-4">
                <button class="btn btn-light btn-lg me-3" onclick="shareCreation()">
                    <i class="fas fa-upload"></i> شارك عملاً إبداعياً
                </button>
                <button class="btn btn-outline-light btn-lg" onclick="joinCommunity()">
                    <i class="fas fa-users"></i> انضم لمجتمع المبدعين
                </button>
            </div>
        </div>
    </section>

<?php
// تضمين الفوتر
include 'includes/footer.php';
?>
    
    <script>
        function startActivity(activityType) {
            switch(activityType) {
                case 'drawing':
                    alert('🎨 مرحباً بك في عالم الرسم! سنفتح أدوات الرسم قريباً...');
                    break;
                case 'music':
                    alert('🎵 استعد للاستماع إلى أجمل الأناشيد التعليمية!');
                    break;
                case 'videos':
                    alert('🎬 فيديوهات ممتعة ومفيدة في انتظارك!');
                    break;
                case 'photography':
                    alert('📸 شارك أجمل لحظاتك معنا!');
                    break;
            }
        }
        
        function shareCreation() {
            <?php if (is_logged_in()): ?>
                alert('سيتم فتح نافذة رفع الأعمال الإبداعية قريباً!');
            <?php else: ?>
                if (confirm('يجب تسجيل الدخول أولاً لمشاركة الأعمال الإبداعية. هل تريد تسجيل الدخول الآن؟')) {
                    window.location.href = 'login.php';
                }
            <?php endif; ?>
        }
        
        function joinCommunity() {
            alert('مجتمع المبدعين قادم قريباً! ترقبوا المزيد من المفاجآت.');
        }
        
        // تأثيرات بصرية للمعرض
        document.addEventListener('DOMContentLoaded', function() {
            const galleryItems = document.querySelectorAll('.gallery-item');
            
            galleryItems.forEach(item => {
                item.addEventListener('click', function() {
                    // يمكن إضافة مودال لعرض الصورة بحجم أكبر
                    alert('سيتم فتح الصورة بحجم أكبر قريباً!');
                });
            });
        });
    </script>
</body>
</html>

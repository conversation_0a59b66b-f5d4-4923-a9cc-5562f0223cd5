<?php
/**
 * ملف إعداد قاعدة البيانات
 * Database Setup File
 */

require_once 'config.php';
require_once 'db.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إعداد منصة نبراس</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>";
echo "<link href='style.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";

echo "<div class='container mt-5'>";
echo "<div class='row justify-content-center'>";
echo "<div class='col-md-8'>";
echo "<div class='card'>";
echo "<div class='card-body'>";
echo "<h2 class='text-center mb-4'>إعداد منصة نبراس</h2>";

try {
    // إنشاء قاعدة البيانات والجداول
    $database->createDatabase();
    
    echo "<div class='alert alert-success'>";
    echo "<h4>✅ تم إعداد قاعدة البيانات بنجاح!</h4>";
    echo "</div>";
    
    // إدراج البيانات التجريبية
    $database->insertSampleData();
    
    echo "<div class='alert alert-info'>";
    echo "<h4>📊 تم إدراج البيانات التجريبية</h4>";
    echo "<p><strong>بيانات تسجيل الدخول للمدير:</strong></p>";
    echo "<p>البريد الإلكتروني: <EMAIL></p>";
    echo "<p>كلمة المرور: admin123</p>";
    echo "</div>";
    
    // إنشاء مجلد الرفع
    if (!file_exists('uploads')) {
        mkdir('uploads', 0777, true);
        echo "<div class='alert alert-success'>تم إنشاء مجلد الرفع</div>";
    }
    
    // إنشاء مجلدات فرعية
    $folders = ['images', 'documents', 'audio', 'video'];
    foreach ($folders as $folder) {
        $path = 'uploads/' . $folder;
        if (!file_exists($path)) {
            mkdir($path, 0777, true);
            echo "<div class='alert alert-success'>تم إنشاء مجلد: $folder</div>";
        }
    }
    
    echo "<div class='text-center mt-4'>";
    echo "<a href='index.php' class='btn btn-primary btn-lg'>الذهاب إلى الصفحة الرئيسية</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>❌ حدث خطأ أثناء الإعداد</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
    
    echo "<div class='alert alert-warning'>";
    echo "<h4>تحقق من الإعدادات التالية:</h4>";
    echo "<ul>";
    echo "<li>تأكد من تشغيل خادم MySQL</li>";
    echo "<li>تحقق من بيانات الاتصال في ملف config.php</li>";
    echo "<li>تأكد من وجود صلاحيات إنشاء قواعد البيانات</li>";
    echo "</ul>";
    echo "</div>";
}

echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "</body>";
echo "</html>";
?>

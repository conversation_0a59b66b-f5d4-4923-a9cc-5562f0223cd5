<?php
require_once 'config.php';
require_once 'db.php';

// جلب المقالات
$articles_query = "SELECT a.*, u.name as author_name FROM articles a 
                   JOIN users u ON a.author_id = u.id 
                   WHERE a.is_published = 1 
                   ORDER BY a.created_at DESC";
$articles_stmt = $db->prepare($articles_query);
$articles_stmt->execute();
$articles = $articles_stmt->fetchAll();

// جلب الوسائط
$media_query = "SELECT * FROM media WHERE is_public = 1 ORDER BY created_at DESC";
$media_stmt = $db->prepare($media_query);
$media_stmt->execute();
$media_items = $media_stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مكتبة المنصة - <?php echo SITE_NAME; ?></title>
    <meta name="description" content="مكتبة شاملة من الموارد التعليمية والمقالات المتخصصة في التوحد">
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="style.css" rel="stylesheet">
    <!-- RTL Enhancements -->
    <link href="rtl-enhancements.css" rel="stylesheet">
</head>
<body>
    <!-- الشريط العلوي -->
    <header class="header">
        <div class="container">
            <a href="index.php" class="logo">
                <i class="fas fa-star"></i> نبراس
            </a>
            <nav>
                <ul class="nav-menu">
                    <li><a href="index.php">الرئيسية</a></li>
                    <li><a href="about.php">من نحن</a></li>
                    <li><a href="library.php" class="active">المكتبة</a></li>
                    <li><a href="workshops.php">الورشات</a></li>
                    <li><a href="contact.php">اتصل بنا</a></li>
                    <li><a href="login.php" class="btn btn-secondary">تسجيل الدخول</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- قسم العنوان -->
    <section class="hero-section" style="padding: 2rem 0;">
        <div class="container">
            <div class="hero-content">
                <h1>مكتبة المنصة</h1>
                <p>مجموعة شاملة من الموارد التعليمية والمقالات المتخصصة</p>
            </div>
        </div>
    </section>

    <!-- شريط البحث -->
    <section class="section" style="padding: 2rem 0;">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="search-arabic">
                        <input type="text" class="form-control" id="searchInput" placeholder="ابحث في المكتبة...">
                        <i class="fas fa-search search-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- التبويبات -->
    <section class="section">
        <div class="container">
            <ul class="nav nav-tabs nav-tabs-arabic justify-content-center mb-4" id="libraryTabs">
                <li class="nav-item">
                    <button class="nav-link active" data-bs-toggle="tab" data-bs-target="#articles">
                        <i class="fas fa-newspaper"></i> المقالات
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" data-bs-toggle="tab" data-bs-target="#books">
                        <i class="fas fa-book"></i> الكتب
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" data-bs-toggle="tab" data-bs-target="#audio">
                        <i class="fas fa-headphones"></i> البودكاست
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" data-bs-toggle="tab" data-bs-target="#videos">
                        <i class="fas fa-video"></i> الفيديوهات
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" data-bs-toggle="tab" data-bs-target="#stories">
                        <i class="fas fa-book-open"></i> القصص
                    </button>
                </li>
            </ul>

            <div class="tab-content">
                <!-- تبويب المقالات -->
                <div class="tab-pane fade show active" id="articles">
                    <div class="row">
                        <?php if (!empty($articles)): ?>
                            <?php foreach ($articles as $article): ?>
                                <div class="col-lg-4 col-md-6 mb-4">
                                    <div class="card card-arabic h-100">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <span class="badge badge-info-arabic"><?php echo htmlspecialchars($article['category']); ?></span>
                                                <small class="text-muted"><?php echo date('Y/m/d', strtotime($article['created_at'])); ?></small>
                                            </div>
                                            <h5 class="card-title"><?php echo htmlspecialchars($article['title']); ?></h5>
                                            <p class="card-text"><?php echo substr(htmlspecialchars($article['content']), 0, 150) . '...'; ?></p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">
                                                    <i class="fas fa-user"></i> <?php echo htmlspecialchars($article['author_name']); ?>
                                                </small>
                                                <a href="article.php?id=<?php echo $article['id']; ?>" class="btn btn-primary btn-sm">
                                                    قراءة المزيد
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="col-12">
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-info-circle"></i>
                                    لا توجد مقالات متاحة حالياً
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- تبويب الكتب -->
                <div class="tab-pane fade" id="books">
                    <div class="row">
                        <?php 
                        $books = array_filter($media_items, function($item) {
                            return $item['file_type'] == 'pdf' || $item['file_type'] == 'document';
                        });
                        ?>
                        <?php if (!empty($books)): ?>
                            <?php foreach ($books as $book): ?>
                                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                                    <div class="card card-arabic h-100 text-center">
                                        <div class="card-body">
                                            <i class="fas fa-file-pdf fa-3x text-danger mb-3"></i>
                                            <h6 class="card-title"><?php echo htmlspecialchars($book['title']); ?></h6>
                                            <p class="card-text small"><?php echo htmlspecialchars($book['description']); ?></p>
                                            <div class="mt-auto">
                                                <span class="badge badge-success-arabic mb-2"><?php echo htmlspecialchars($book['category']); ?></span>
                                                <br>
                                                <a href="<?php echo htmlspecialchars($book['file_path']); ?>" class="btn btn-primary btn-sm" target="_blank">
                                                    <i class="fas fa-download"></i> تحميل
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="col-12">
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-info-circle"></i>
                                    لا توجد كتب متاحة حالياً
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- تبويب البودكاست -->
                <div class="tab-pane fade" id="audio">
                    <div class="row">
                        <?php 
                        $audio_items = array_filter($media_items, function($item) {
                            return $item['file_type'] == 'audio';
                        });
                        ?>
                        <?php if (!empty($audio_items)): ?>
                            <?php foreach ($audio_items as $audio): ?>
                                <div class="col-lg-6 mb-4">
                                    <div class="card card-arabic">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    <i class="fas fa-music fa-2x text-primary"></i>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <h6 class="card-title mb-1"><?php echo htmlspecialchars($audio['title']); ?></h6>
                                                    <p class="card-text small mb-2"><?php echo htmlspecialchars($audio['description']); ?></p>
                                                    <span class="badge badge-warning-arabic"><?php echo htmlspecialchars($audio['category']); ?></span>
                                                </div>
                                                <div>
                                                    <button class="btn btn-primary btn-sm" onclick="playAudio('<?php echo htmlspecialchars($audio['file_path']); ?>')">
                                                        <i class="fas fa-play"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="col-12">
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-info-circle"></i>
                                    لا توجد ملفات صوتية متاحة حالياً
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- تبويب الفيديوهات -->
                <div class="tab-pane fade" id="videos">
                    <div class="row">
                        <?php 
                        $videos = array_filter($media_items, function($item) {
                            return $item['file_type'] == 'video';
                        });
                        ?>
                        <?php if (!empty($videos)): ?>
                            <?php foreach ($videos as $video): ?>
                                <div class="col-lg-4 col-md-6 mb-4">
                                    <div class="card card-arabic">
                                        <div class="card-body text-center">
                                            <i class="fas fa-play-circle fa-4x text-primary mb-3"></i>
                                            <h6 class="card-title"><?php echo htmlspecialchars($video['title']); ?></h6>
                                            <p class="card-text small"><?php echo htmlspecialchars($video['description']); ?></p>
                                            <span class="badge badge-info-arabic mb-3"><?php echo htmlspecialchars($video['category']); ?></span>
                                            <br>
                                            <button class="btn btn-primary btn-sm" onclick="playVideo('<?php echo htmlspecialchars($video['file_path']); ?>')">
                                                <i class="fas fa-play"></i> مشاهدة
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="col-12">
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-info-circle"></i>
                                    لا توجد فيديوهات متاحة حالياً
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- تبويب القصص -->
                <div class="tab-pane fade" id="stories">
                    <div class="row">
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card card-arabic h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-book-open fa-3x text-success mb-3"></i>
                                    <h6 class="card-title">قصة أحمد والألوان</h6>
                                    <p class="card-text">قصة تفاعلية تعلم الأطفال الألوان بطريقة ممتعة</p>
                                    <span class="badge badge-success-arabic mb-3">قصص تعليمية</span>
                                    <br>
                                    <button class="btn btn-success btn-sm">
                                        <i class="fas fa-book-reader"></i> قراءة القصة
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card card-arabic h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-book-open fa-3x text-warning mb-3"></i>
                                    <h6 class="card-title">مغامرة في عالم الأرقام</h6>
                                    <p class="card-text">قصة شيقة لتعلم الأرقام والعد</p>
                                    <span class="badge badge-warning-arabic mb-3">رياضيات</span>
                                    <br>
                                    <button class="btn btn-warning btn-sm">
                                        <i class="fas fa-book-reader"></i> قراءة القصة
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card card-arabic h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-book-open fa-3x text-info mb-3"></i>
                                    <h6 class="card-title">صديقي الجديد</h6>
                                    <p class="card-text">قصة عن الصداقة والتعامل مع الآخرين</p>
                                    <span class="badge badge-info-arabic mb-3">مهارات اجتماعية</span>
                                    <br>
                                    <button class="btn btn-info btn-sm">
                                        <i class="fas fa-book-reader"></i> قراءة القصة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- التذييل -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div>
                    <h4>منصة نبراس</h4>
                    <p>منصة تعليمية متخصصة في دعم الأطفال ذوي اضطراب طيف التوحد وأسرهم</p>
                </div>
                <div>
                    <h4>روابط سريعة</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li><a href="index.php" style="color: #adb5bd; text-decoration: none;">الرئيسية</a></li>
                        <li><a href="about.php" style="color: #adb5bd; text-decoration: none;">من نحن</a></li>
                        <li><a href="workshops.php" style="color: #adb5bd; text-decoration: none;">الورشات</a></li>
                        <li><a href="contact.php" style="color: #adb5bd; text-decoration: none;">اتصل بنا</a></li>
                    </ul>
                </div>
                <div>
                    <h4>تواصل معنا</h4>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +216 XX XXX XXX</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 منصة نبراس. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // وظيفة البحث
        document.getElementById('searchInput').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const cards = document.querySelectorAll('.card');
            
            cards.forEach(card => {
                const title = card.querySelector('.card-title');
                const text = card.querySelector('.card-text');
                
                if (title && text) {
                    const titleText = title.textContent.toLowerCase();
                    const bodyText = text.textContent.toLowerCase();
                    
                    if (titleText.includes(searchTerm) || bodyText.includes(searchTerm)) {
                        card.closest('.col-lg-4, .col-lg-3, .col-lg-6, .col-md-6, .col-md-4, .col-sm-6').style.display = 'block';
                    } else {
                        card.closest('.col-lg-4, .col-lg-3, .col-lg-6, .col-md-6, .col-md-4, .col-sm-6').style.display = 'none';
                    }
                }
            });
        });

        // وظيفة تشغيل الصوت
        function playAudio(audioPath) {
            // يمكن تطوير هذه الوظيفة لاحقاً
            alert('سيتم تشغيل الملف الصوتي: ' + audioPath);
        }

        // وظيفة تشغيل الفيديو
        function playVideo(videoPath) {
            // يمكن تطوير هذه الوظيفة لاحقاً
            alert('سيتم تشغيل الفيديو: ' + videoPath);
        }
    </script>
</body>
</html>

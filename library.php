<?php
require_once 'config.php';
require_once 'db.php';

// إعداد متغيرات الصفحة
$page_title = 'مكتبة المنصة';
$page_description = 'مكتبة شاملة من الموارد التعليمية والمقالات المتخصصة في التوحد';

// جلب المقالات
$articles_query = "SELECT a.*, u.name as author_name FROM articles a
                   JOIN users u ON a.author_id = u.id
                   WHERE a.is_published = 1
                   ORDER BY a.created_at DESC";
$articles_stmt = $db->prepare($articles_query);
$articles_stmt->execute();
$articles = $articles_stmt->fetchAll();

// جلب الوسائط
$media_query = "SELECT * FROM media WHERE is_public = 1 ORDER BY created_at DESC";
$media_stmt = $db->prepare($media_query);
$media_stmt->execute();
$media_items = $media_stmt->fetchAll();

// تضمين الهيدر
include 'includes/header.php';
?>

    <style>
        .library-header {
            background: linear-gradient(135deg, #4caf50, #2e7d32, #1b5e20, #66bb6a);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
            color: white;
            padding: 3rem 0;
            border-radius: 0 0 30px 30px;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .floating-shapes {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            overflow: hidden;
            pointer-events: none;
        }

        .shape {
            position: absolute;
            color: rgba(255,255,255,0.1);
            animation: float 6s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            top: 60%;
            right: 15%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            bottom: 30%;
            left: 20%;
            animation-delay: 4s;
        }

        .shape:nth-child(4) {
            top: 40%;
            right: 30%;
            animation-delay: 6s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>

    <!-- قسم العنوان المحسن -->
    <section class="library-header position-relative">
        <div class="floating-shapes">
            <div class="shape"><i class="fas fa-book fa-3x"></i></div>
            <div class="shape"><i class="fas fa-file-alt fa-3x"></i></div>
            <div class="shape"><i class="fas fa-headphones fa-3x"></i></div>
            <div class="shape"><i class="fas fa-video fa-3x"></i></div>
        </div>
        <div class="container">
            <div class="hero-content text-center">
                <h1><i class="fas fa-book"></i> مكتبة المنصة</h1>
                <p class="lead">مجموعة شاملة من الموارد التعليمية والمقالات المتخصصة</p>
                <div class="mt-4">
                    <span class="badge bg-light text-dark fs-6 me-2">
                        <i class="fas fa-file-alt"></i> مقالات
                    </span>
                    <span class="badge bg-light text-dark fs-6 me-2">
                        <i class="fas fa-headphones"></i> بودكاست
                    </span>
                    <span class="badge bg-light text-dark fs-6 me-2">
                        <i class="fas fa-video"></i> فيديوهات
                    </span>
                    <span class="badge bg-light text-dark fs-6">
                        <i class="fas fa-download"></i> تحميلات
                    </span>
                </div>
            </div>
        </div>
    </section>

    <!-- شريط البحث -->
    <section class="section" style="padding: 2rem 0;">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="search-arabic">
                        <input type="text" class="form-control" id="searchInput" placeholder="ابحث في المكتبة...">
                        <i class="fas fa-search search-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- التبويبات -->
    <section class="section">
        <div class="container">
            <ul class="nav nav-tabs nav-tabs-arabic justify-content-center mb-4" id="libraryTabs">
                <li class="nav-item">
                    <button class="nav-link active" data-bs-toggle="tab" data-bs-target="#articles">
                        <i class="fas fa-newspaper"></i> المقالات
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" data-bs-toggle="tab" data-bs-target="#books">
                        <i class="fas fa-book"></i> الكتب
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" data-bs-toggle="tab" data-bs-target="#audio">
                        <i class="fas fa-headphones"></i> البودكاست
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" data-bs-toggle="tab" data-bs-target="#videos">
                        <i class="fas fa-video"></i> الفيديوهات
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" data-bs-toggle="tab" data-bs-target="#stories">
                        <i class="fas fa-book-open"></i> القصص
                    </button>
                </li>
            </ul>

            <div class="tab-content">
                <!-- تبويب المقالات -->
                <div class="tab-pane fade show active" id="articles">
                    <div class="row">
                        <?php if (!empty($articles)): ?>
                            <?php foreach ($articles as $article): ?>
                                <div class="col-lg-4 col-md-6 mb-4">
                                    <div class="card card-arabic h-100">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <span class="badge badge-info-arabic"><?php echo htmlspecialchars($article['category']); ?></span>
                                                <small class="text-muted"><?php echo date('Y/m/d', strtotime($article['created_at'])); ?></small>
                                            </div>
                                            <h5 class="card-title"><?php echo htmlspecialchars($article['title']); ?></h5>
                                            <p class="card-text"><?php echo substr(htmlspecialchars($article['content']), 0, 150) . '...'; ?></p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">
                                                    <i class="fas fa-user"></i> <?php echo htmlspecialchars($article['author_name']); ?>
                                                </small>
                                                <a href="article.php?id=<?php echo $article['id']; ?>" class="btn btn-primary btn-sm">
                                                    قراءة المزيد
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="col-12">
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-info-circle"></i>
                                    لا توجد مقالات متاحة حالياً
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- تبويب الكتب -->
                <div class="tab-pane fade" id="books">
                    <div class="row">
                        <?php 
                        $books = array_filter($media_items, function($item) {
                            return $item['file_type'] == 'pdf' || $item['file_type'] == 'document';
                        });
                        ?>
                        <?php if (!empty($books)): ?>
                            <?php foreach ($books as $book): ?>
                                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                                    <div class="card card-arabic h-100 text-center">
                                        <div class="card-body">
                                            <i class="fas fa-file-pdf fa-3x text-danger mb-3"></i>
                                            <h6 class="card-title"><?php echo htmlspecialchars($book['title']); ?></h6>
                                            <p class="card-text small"><?php echo htmlspecialchars($book['description']); ?></p>
                                            <div class="mt-auto">
                                                <span class="badge badge-success-arabic mb-2"><?php echo htmlspecialchars($book['category']); ?></span>
                                                <br>
                                                <a href="<?php echo htmlspecialchars($book['file_path']); ?>" class="btn btn-primary btn-sm" target="_blank">
                                                    <i class="fas fa-download"></i> تحميل
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="col-12">
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-info-circle"></i>
                                    لا توجد كتب متاحة حالياً
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- تبويب البودكاست -->
                <div class="tab-pane fade" id="audio">
                    <div class="row">
                        <?php 
                        $audio_items = array_filter($media_items, function($item) {
                            return $item['file_type'] == 'audio';
                        });
                        ?>
                        <?php if (!empty($audio_items)): ?>
                            <?php foreach ($audio_items as $audio): ?>
                                <div class="col-lg-6 mb-4">
                                    <div class="card card-arabic">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    <i class="fas fa-music fa-2x text-primary"></i>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <h6 class="card-title mb-1"><?php echo htmlspecialchars($audio['title']); ?></h6>
                                                    <p class="card-text small mb-2"><?php echo htmlspecialchars($audio['description']); ?></p>
                                                    <span class="badge badge-warning-arabic"><?php echo htmlspecialchars($audio['category']); ?></span>
                                                </div>
                                                <div>
                                                    <button class="btn btn-primary btn-sm" onclick="playAudio('<?php echo htmlspecialchars($audio['file_path']); ?>')">
                                                        <i class="fas fa-play"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="col-12">
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-info-circle"></i>
                                    لا توجد ملفات صوتية متاحة حالياً
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- تبويب الفيديوهات -->
                <div class="tab-pane fade" id="videos">
                    <div class="row">
                        <?php 
                        $videos = array_filter($media_items, function($item) {
                            return $item['file_type'] == 'video';
                        });
                        ?>
                        <?php if (!empty($videos)): ?>
                            <?php foreach ($videos as $video): ?>
                                <div class="col-lg-4 col-md-6 mb-4">
                                    <div class="card card-arabic">
                                        <div class="card-body text-center">
                                            <i class="fas fa-play-circle fa-4x text-primary mb-3"></i>
                                            <h6 class="card-title"><?php echo htmlspecialchars($video['title']); ?></h6>
                                            <p class="card-text small"><?php echo htmlspecialchars($video['description']); ?></p>
                                            <span class="badge badge-info-arabic mb-3"><?php echo htmlspecialchars($video['category']); ?></span>
                                            <br>
                                            <button class="btn btn-primary btn-sm" onclick="playVideo('<?php echo htmlspecialchars($video['file_path']); ?>')">
                                                <i class="fas fa-play"></i> مشاهدة
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="col-12">
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-info-circle"></i>
                                    لا توجد فيديوهات متاحة حالياً
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- تبويب القصص -->
                <div class="tab-pane fade" id="stories">
                    <div class="row">
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card card-arabic h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-book-open fa-3x text-success mb-3"></i>
                                    <h6 class="card-title">قصة أحمد والألوان</h6>
                                    <p class="card-text">قصة تفاعلية تعلم الأطفال الألوان بطريقة ممتعة</p>
                                    <span class="badge badge-success-arabic mb-3">قصص تعليمية</span>
                                    <br>
                                    <button class="btn btn-success btn-sm">
                                        <i class="fas fa-book-reader"></i> قراءة القصة
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card card-arabic h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-book-open fa-3x text-warning mb-3"></i>
                                    <h6 class="card-title">مغامرة في عالم الأرقام</h6>
                                    <p class="card-text">قصة شيقة لتعلم الأرقام والعد</p>
                                    <span class="badge badge-warning-arabic mb-3">رياضيات</span>
                                    <br>
                                    <button class="btn btn-warning btn-sm">
                                        <i class="fas fa-book-reader"></i> قراءة القصة
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card card-arabic h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-book-open fa-3x text-info mb-3"></i>
                                    <h6 class="card-title">صديقي الجديد</h6>
                                    <p class="card-text">قصة عن الصداقة والتعامل مع الآخرين</p>
                                    <span class="badge badge-info-arabic mb-3">مهارات اجتماعية</span>
                                    <br>
                                    <button class="btn btn-info btn-sm">
                                        <i class="fas fa-book-reader"></i> قراءة القصة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

<?php
// إضافة JavaScript خاص بالصفحة
$page_js = "
    // تهيئة البحث في المكتبة
    PageSpecific.library.initSearch();

    // وظيفة تشغيل الصوت
    function playAudio(audioPath) {
        NibrassHelpers.showAlert('سيتم تشغيل الملف الصوتي: ' + audioPath, 'info');
    }

    // وظيفة تشغيل الفيديو
    function playVideo(videoPath) {
        NibrassHelpers.showAlert('سيتم تشغيل الفيديو: ' + videoPath, 'info');
    }
";

// تضمين الفوتر
include 'includes/footer.php';
?>
